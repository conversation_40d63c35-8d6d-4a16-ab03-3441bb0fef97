{% extends 'base.html' %}

{% block title %}Add New Warehouse - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
<style>
    .main-content {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin: 20px;
        padding: 30px;
    }
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.4);
    }
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    .card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border-radius: 15px 15px 0 0 !important;
        padding: 20px;
    }
    .form-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    .required {
        color: #dc3545;
    }
    .upload-area {
        border: 2px dashed #007bff;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        background: #f8f9ff;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .upload-area:hover {
        background: #e3f2fd;
        border-color: #0056b3;
    }
    .upload-area.dragover {
        background: #e3f2fd;
        border-color: #0056b3;
        transform: scale(1.02);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="main-content">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="text-primary mb-1">
                    <i class="fas fa-warehouse"></i> Add New Warehouse
                </h2>
                <p class="text-muted mb-0">Register a new warehouse location with complete details</p>
            </div>
            <div>
                <a href="/warehouse-management/manage" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Warehouses
                </a>
            </div>
        </div>

        <!-- Add Warehouse Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus-circle"></i> Warehouse Information</h5>
            </div>
            <div class="card-body">
                <form method="post" action="/warehouse-management/add" enctype="multipart/form-data">
                    
                    <!-- Basic Information -->
                    <h6 class="text-primary mb-3"><i class="fas fa-info-circle"></i> Basic Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Warehouse Name <span class="required">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       placeholder="Enter warehouse name (e.g., Karachi Main Warehouse)" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="city">City <span class="required">*</span></label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       placeholder="Enter city name" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="address">Address</label>
                                <textarea class="form-control" id="address" name="address" rows="3"
                                          placeholder="Enter complete warehouse address"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="country">Country</label>
                                <input type="text" class="form-control" id="country" name="country" 
                                       placeholder="Enter country" value="Pakistan">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="capacity">Capacity (sq ft)</label>
                                <input type="number" class="form-control" id="capacity" name="capacity" 
                                       placeholder="Enter warehouse capacity in square feet">
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <hr class="my-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-address-book"></i> Contact Information</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="manager">Manager Name</label>
                                <input type="text" class="form-control" id="manager" name="manager" 
                                       placeholder="Enter manager name">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       placeholder="Enter phone number">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="Enter email address">
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <hr class="my-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-file-alt"></i> Additional Information</h6>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"
                                          placeholder="Enter warehouse description, facilities, or special notes"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Image Upload -->
                    <hr class="my-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-image"></i> Warehouse Image (Optional)</h6>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="upload-area" onclick="document.getElementById('warehouse_image').click()">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <h5>Click to upload warehouse image</h5>
                                <p class="text-muted">PNG, JPG, JPEG, GIF, WebP files up to 16MB</p>
                                <input type="file" id="warehouse_image" name="warehouse_image" 
                                       accept="image/*" style="display: none;">
                            </div>
                            <div id="image-preview" class="mt-3" style="display: none;">
                                <img id="preview-img" src="" alt="Preview" class="img-thumbnail" style="max-height: 200px;">
                                <button type="button" class="btn btn-sm btn-danger ml-2" onclick="removeImage()">
                                    <i class="fas fa-times"></i> Remove
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <hr class="my-4">
                    <div class="d-flex justify-content-end">
                        <a href="/warehouse-management/manage" class="btn btn-outline-secondary me-3">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Add Warehouse
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Image upload preview
document.getElementById('warehouse_image').addEventListener('change', function(e) {
    if (e.target.files.length > 0) {
        const file = e.target.files[0];
        
        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('Invalid file type. Please upload PNG, JPG, JPEG, GIF, or WebP images.');
            return;
        }
        
        // Validate file size (16MB)
        if (file.size > 16 * 1024 * 1024) {
            alert('File size too large. Please upload images smaller than 16MB.');
            return;
        }
        
        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-img').src = e.target.result;
            document.getElementById('image-preview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

function removeImage() {
    document.getElementById('warehouse_image').value = '';
    document.getElementById('image-preview').style.display = 'none';
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields marked with *');
    }
});
</script>
{% endblock %}
