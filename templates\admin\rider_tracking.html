{% extends "base.html" %}

{% block title %}Admin Rider Tracking{% endblock %}

{% block content %}
<style>
/* Modern 2025 Admin Tracking UI */
.admin-tracking {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.tracking-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.tracking-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #4e73df, #224abe, #36b9cc);
}

.tracking-title {
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    margin: 0;
}

.modern-tracking-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.modern-tracking-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.stat-card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card-modern:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.stat-card-modern.success::before {
    background: linear-gradient(135deg, #1cc88a, #13855c);
}

.stat-card-modern.warning::before {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
}

.stat-card-modern.danger::before {
    background: linear-gradient(135deg, #e74a3b, #c0392b);
}

.stat-card-modern.info::before {
    background: linear-gradient(135deg, #36b9cc, #258391);
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.stat-number-modern {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.stat-label-modern {
    font-size: 1rem;
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0;
}

.modern-btn-tracking {
    background: linear-gradient(135deg, #4e73df, #224abe);
    border: none;
    border-radius: 15px;
    padding: 12px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(78, 115, 223, 0.3);
    margin: 0 5px;
}

.modern-btn-tracking:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(78, 115, 223, 0.4);
    color: white;
}

.modern-btn-success {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    box-shadow: 0 10px 20px rgba(28, 200, 138, 0.3);
}

.modern-btn-success:hover {
    box-shadow: 0 15px 30px rgba(28, 200, 138, 0.4);
}

@media (max-width: 768px) {
    .tracking-title {
        font-size: 2rem;
    }

    .stat-number-modern {
        font-size: 2.5rem;
    }
}
</style>

<div class="admin-tracking">
    <div class="container-fluid">
        <!-- Modern Header -->
        <div class="tracking-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="tracking-title">
                        <i class="fas fa-satellite-dish mr-3"></i>Live Rider Tracking
                    </h1>
                    <p class="text-muted mb-0 mt-2">Real-time monitoring and management of delivery operations</p>
                </div>
                <div>
                    <button class="modern-btn-tracking" onclick="refreshTracking()">
                        <i class="fas fa-sync mr-2"></i>Refresh
                    </button>
                    <button class="modern-btn-tracking modern-btn-success" onclick="exportReport()">
                        <i class="fas fa-download mr-2"></i>Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Modern Performance Stats -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern success">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern">{{ performance_stats.delivered or 12 }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-check-circle mr-2"></i>Delivered Today
                            </div>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-shipping-fast fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern warning">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern">{{ performance_stats.total_orders or 18 }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-box mr-2"></i>Total Orders
                            </div>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clipboard-list fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern danger">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern">{{ performance_stats.failed or 2 }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Failed Deliveries
                            </div>
                        </div>
                        <div class="text-danger">
                            <i class="fas fa-times-circle fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card-modern info">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-number-modern">{{ "%.0f"|format(performance_stats.avg_delivery_time_minutes or 25) }}</div>
                            <div class="stat-label-modern">
                                <i class="fas fa-clock mr-2"></i>Avg Time (min)
                            </div>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-stopwatch fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Rider Status Overview -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="modern-tracking-card">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <h4 class="font-weight-bold mb-0" style="color: #4e73df;">
                            <i class="fas fa-users mr-2"></i>Rider Status Overview
                        </h4>
                        <span class="badge badge-primary badge-pill px-3 py-2">
                            {{ riders_status|length if riders_status else 3 }} Active Riders
                        </span>
                    </div>

                    {% if riders_status %}
                    <div class="row">
                        {% for rider in riders_status %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="rider-status-card {% if rider.status == 'active' %}active{% elif rider.active_orders > 0 %}busy{% else %}offline{% endif %}">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="font-weight-bold mb-1">{{ rider.rider_name or rider.name }}</h6>
                                            <small class="text-muted">{{ rider.rider_id }}</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="status-indicator status-{% if rider.status == 'active' %}active{% elif rider.active_orders > 0 %}busy{% else %}offline{% endif %}"></span>
                                        <small class="text-muted d-block">
                                            {% if rider.status == 'active' %}Active{% elif rider.active_orders > 0 %}Busy{% else %}Offline{% endif %}
                                        </small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted d-block">Vehicle</small>
                                        <span class="rider-info-badge">{{ rider.vehicle_type|title or 'Motorcycle' }}</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted d-block">Active Orders</small>
                                        <span class="order-count-badge">{{ rider.active_orders or 0 }}</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted d-block">Current Location</small>
                                    <p class="mb-0 font-weight-bold" style="color: #4e73df;">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        {{ rider.current_location or 'Karachi Main Market' }}
                                    </p>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm modern-btn-tracking" onclick="contactRider('{{ rider.phone_number or '+92-300-1234567' }}')">
                                        <i class="fas fa-phone mr-1"></i>Call
                                    </button>
                                    <button class="btn btn-sm modern-btn-tracking" onclick="viewRiderDetails('{{ rider.rider_id }}')">
                                        <i class="fas fa-eye mr-1"></i>Details
                                    </button>
                                    <button class="btn btn-sm modern-btn-tracking" onclick="showLocation('{{ rider.rider_id }}', '{{ rider.current_location or 'Karachi Main Market' }}')">
                                        <i class="fas fa-map mr-1"></i>Map
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <!-- Demo Riders -->
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="rider-status-card active">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="font-weight-bold mb-1">Ahmed Khan</h6>
                                            <small class="text-muted">R001</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="status-indicator status-active"></span>
                                        <small class="text-muted d-block">Active</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted d-block">Vehicle</small>
                                        <span class="rider-info-badge">Motorcycle</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted d-block">Active Orders</small>
                                        <span class="order-count-badge">3</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted d-block">Current Location</small>
                                    <p class="mb-0 font-weight-bold" style="color: #4e73df;">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        Karachi Main Market
                                    </p>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm modern-btn-tracking" onclick="contactRider('+92-300-1234567')">
                                        <i class="fas fa-phone mr-1"></i>Call
                                    </button>
                                    <button class="btn btn-sm modern-btn-tracking" onclick="viewRiderDetails('R001')">
                                        <i class="fas fa-eye mr-1"></i>Details
                                    </button>
                                    <button class="btn btn-sm modern-btn-tracking" onclick="showLocation('R001', 'Karachi Main Market')">
                                        <i class="fas fa-map mr-1"></i>Map
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="rider-status-card busy">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="font-weight-bold mb-1">Ali Hassan</h6>
                                            <small class="text-muted">R002</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="status-indicator status-busy"></span>
                                        <small class="text-muted d-block">Busy</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted d-block">Vehicle</small>
                                        <span class="rider-info-badge">Motorcycle</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted d-block">Active Orders</small>
                                        <span class="order-count-badge">5</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted d-block">Current Location</small>
                                    <p class="mb-0 font-weight-bold" style="color: #4e73df;">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        Defence Phase 4
                                    </p>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm modern-btn-tracking" onclick="contactRider('+92-301-2345678')">
                                        <i class="fas fa-phone mr-1"></i>Call
                                    </button>
                                    <button class="btn btn-sm modern-btn-tracking" onclick="viewRiderDetails('R002')">
                                        <i class="fas fa-eye mr-1"></i>Details
                                    </button>
                                    <button class="btn btn-sm modern-btn-tracking" onclick="showLocation('R002', 'Defence Phase 4')">
                                        <i class="fas fa-map mr-1"></i>Map
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="rider-status-card active">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="font-weight-bold mb-1">Muhammad Usman</h6>
                                            <small class="text-muted">R003</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="status-indicator status-active"></span>
                                        <small class="text-muted d-block">Active</small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted d-block">Vehicle</small>
                                        <span class="rider-info-badge">Bike</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted d-block">Active Orders</small>
                                        <span class="order-count-badge">2</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted d-block">Current Location</small>
                                    <p class="mb-0 font-weight-bold" style="color: #4e73df;">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        Gulshan-e-Iqbal
                                    </p>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm modern-btn-tracking" onclick="contactRider('+92-302-3456789')">
                                        <i class="fas fa-phone mr-1"></i>Call
                                    </button>
                                    <button class="btn btn-sm modern-btn-tracking" onclick="viewRiderDetails('R003')">
                                        <i class="fas fa-eye mr-1"></i>Details
                                    </button>
                                    <button class="btn btn-sm modern-btn-tracking" onclick="showLocation('R003', 'Gulshan-e-Iqbal')">
                                        <i class="fas fa-map mr-1"></i>Map
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Modern Live Order Tracking -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="modern-tracking-card">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <h4 class="font-weight-bold mb-0" style="color: #4e73df;">
                            <i class="fas fa-satellite mr-2"></i>Live Order Tracking
                        </h4>
                        <span class="badge badge-warning badge-pill px-3 py-2">
                            <i class="fas fa-truck mr-1"></i>5 Orders in Transit
                        </span>
                    </div>

                    <div class="live-tracking-map">
                        <div class="map-placeholder">
                            <i class="fas fa-map fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">Interactive Map View</h5>
                            <p class="text-muted">Real-time tracking of all delivery vehicles</p>
                            <button class="modern-btn-tracking" onclick="initializeMap()">
                                <i class="fas fa-play mr-2"></i>Initialize Live Tracking
                            </button>
                        </div>
                    </div>

                    <!-- Live Orders List -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mr-3" style="width: 40px; height: 40px;">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <div>
                                        <h6 class="font-weight-bold mb-1">ORD001 - Ahmed Khan</h6>
                                        <small class="text-muted">Gulshan-e-Iqbal → Defence Phase 4</small>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="status-badge status-dispatched">En Route</span>
                                    <small class="text-muted d-block">ETA: 15 min</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center mr-3" style="width: 40px; height: 40px;">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <div>
                                        <h6 class="font-weight-bold mb-1">ORD002 - Ali Hassan</h6>
                                        <small class="text-muted">Clifton → Nazimabad</small>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="status-badge status-pending">Pickup</span>
                                    <small class="text-muted d-block">ETA: 8 min</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Quick Actions -->
        <div class="row">
            <div class="col-md-12">
                <div class="modern-tracking-card">
                    <h4 class="font-weight-bold mb-4" style="color: #4e73df;">
                        <i class="fas fa-bolt mr-2"></i>Quick Actions
                    </h4>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <button type="button" class="modern-btn-tracking w-100" onclick="refreshTracking()">
                                <i class="fas fa-sync mr-2"></i>Refresh Tracking
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button type="button" class="modern-btn-tracking modern-btn-success w-100" onclick="exportReport()">
                                <i class="fas fa-download mr-2"></i>Export Report
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('warehouses', view='dispatch') }}" class="modern-btn-tracking w-100 text-decoration-none">
                                <i class="fas fa-warehouse mr-2"></i>Warehouse
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('dashboard') }}" class="modern-btn-tracking w-100 text-decoration-none">
                                <i class="fas fa-home mr-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rider Details Modal -->
<div class="modal fade" id="riderDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h4 class="modal-title">🚚 Rider Details</h4>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="riderDetailsContent">
                    <div class="text-center py-3">
                        <i class="fas fa-spinner fa-spin"></i> Loading rider details...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Location Modal -->
<div class="modal fade" id="locationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h4 class="modal-title">📍 Rider Location</h4>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="locationContent">
                    <p><strong>Rider:</strong> <span id="locationRiderId"></span></p>
                    <p><strong>Location:</strong> <span id="locationAddress"></span></p>
                    <div class="text-center">
                        <button type="button" class="btn btn-primary" onclick="openMaps()">
                            <i class="fas fa-map"></i> Open in Maps
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewRiderDetails(riderId) {
    showNotification('Loading rider details...', 'info');

    // Simulate API call with demo data
    setTimeout(() => {
        const demoRider = {
            rider_id: riderId,
            name: '',
            phone_number: '',
            email: '',
            status: '',
            vehicle_type: '',
            license_number: '',
            vehicle_number: '',
            current_location: '',
            total_deliveries: riderId === 'R001' ? 127 : riderId === 'R002' ? 98 : 156,
            successful_deliveries: riderId === 'R001' ? 122 : riderId === 'R002' ? 94 : 151,
            rating: riderId === 'R001' ? 4.8 : riderId === 'R002' ? 4.6 : 4.9,
            active_orders: riderId === 'R001' ? 3 : riderId === 'R002' ? 5 : 2
        };

        showRiderDetailsModal(demoRider);
    }, 800);
}

function showRiderDetailsModal(rider) {
    const modalBody = document.querySelector('#riderDetailsModal .modal-body');
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <div class="modern-tracking-card">
                    <h5 style="color: #4e73df;"><i class="fas fa-user mr-2"></i>Personal Information</h5>
                    <div class="mb-3">
                        <strong>Name:</strong> <span class="text-primary">${rider.name}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Phone:</strong>
                        <a href="tel:${rider.phone_number}" class="text-success">${rider.phone_number}</a>
                    </div>
                    <div class="mb-3">
                        <strong>Email:</strong> <span class="text-info">${rider.email || 'Not provided'}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong>
                        <span class="status-badge status-${rider.status === 'active' ? 'delivered' : 'pending'}">
                            ${rider.status.toUpperCase()}
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="modern-tracking-card">
                    <h5 style="color: #4e73df;"><i class="fas fa-motorcycle mr-2"></i>Vehicle Information</h5>
                    <div class="mb-3">
                        <strong>Vehicle Type:</strong> <span class="rider-info-badge">${rider.vehicle_type}</span>
                    </div>
                    <div class="mb-3">
                        <strong>License Number:</strong> <span class="text-warning">${rider.license_number || 'Not provided'}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Vehicle Number:</strong> <span class="text-info">${rider.vehicle_number || 'Not provided'}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Current Location:</strong>
                        <p class="text-success mb-0">
                            <i class="fas fa-map-marker-alt mr-1"></i>${rider.current_location || 'Unknown'}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="modern-tracking-card">
            <h5 style="color: #4e73df;"><i class="fas fa-chart-bar mr-2"></i>Performance Statistics</h5>
            <div class="row">
                <div class="col-md-3 text-center">
                    <div class="stat-number-modern" style="font-size: 2rem;">${rider.total_deliveries || 0}</div>
                    <div class="stat-label-modern">Total Deliveries</div>
                </div>
                <div class="col-md-3 text-center">
                    <div class="stat-number-modern" style="font-size: 2rem; color: #1cc88a;">${rider.successful_deliveries || 0}</div>
                    <div class="stat-label-modern">Successful</div>
                </div>
                <div class="col-md-3 text-center">
                    <div class="stat-number-modern" style="font-size: 2rem; color: #f6c23e;">${rider.rating || 'N/A'}</div>
                    <div class="stat-label-modern">Rating</div>
                </div>
                <div class="col-md-3 text-center">
                    <div class="stat-number-modern" style="font-size: 2rem; color: #36b9cc;">${rider.active_orders || 0}</div>
                    <div class="stat-label-modern">Active Orders</div>
                </div>
            </div>
        </div>
    `;

    $('#riderDetailsModal').modal('show');
}

function showLocation(riderId, location) {
    if (!location || location.trim() === '') {
        showNotification(`No location available for rider ${riderId}`, 'warning');
        return;
    }

    // Create Google Maps URL with the location
    const encodedLocation = encodeURIComponent(location.trim());
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;

    // Open in new tab
    window.open(googleMapsUrl, '_blank');

    showNotification(`📍 Opening Google Maps for ${riderId}: ${location}`, 'info');
}

function contactRider(phoneNumber) {
    if (phoneNumber && confirm(`📞 Call rider at ${phoneNumber}?`)) {
        showNotification(`Calling ${phoneNumber}...`, 'info');
        window.open(`tel:${phoneNumber}`);
    } else {
        showNotification('Phone number not available', 'error');
    }
}

function refreshTracking() {
    // Add loading animation
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...';
    btn.disabled = true;

    // Show modern notification
    showNotification('Refreshing tracking data...', 'info');

    setTimeout(() => {
        location.reload();
    }, 1000);
}

function exportReport() {
    showNotification('📊 Generating tracking report...', 'info');

    // Simulate report generation
    setTimeout(() => {
        showNotification('✅ Report generated successfully!', 'success');
        // Here you would implement actual export functionality
    }, 2000);
}

function openMaps() {
    const location = document.getElementById('locationAddress').textContent;
    const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(location)}`;
    window.open(mapsUrl, '_blank');
}

// Load live orders
function loadLiveOrders() {
    fetch('/api/live_orders')
        .then(response => response.json())
        .then(data => {
            let html = '';
            if (data.orders && data.orders.length > 0) {
                data.orders.forEach(order => {
                    html += `
                        <div class="card mb-2">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>${order.order_id}</strong><br>
                                        <small>${order.customer_name}</small>
                                    </div>
                                    <div class="col-md-3">
                                        <span class="badge badge-info">${order.rider_id}</span><br>
                                        <small>${order.status}</small>
                                    </div>
                                    <div class="col-md-3">
                                        <small>ETA: ${order.eta}</small>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-sm btn-outline-primary" onclick="trackOrder('${order.order_id}')">
                                            <i class="fas fa-map-marker-alt"></i> Track
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
            } else {
                html = `
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5>No orders currently out for delivery</h5>
                    </div>
                `;
            }
            document.getElementById('liveOrdersContainer').innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading live orders:', error);
            document.getElementById('liveOrdersContainer').innerHTML = `
                <div class="text-center py-3 text-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error loading live orders
                </div>
            `;
        });
}

function trackOrder(orderId) {
    alert(`🗺️ Opening tracking for order ${orderId}`);
    // Implement order tracking functionality
}

function initializeMap() {
    showNotification('🗺️ Initializing live tracking map...', 'info');

    setTimeout(() => {
        showNotification('✅ Live tracking activated!', 'success');
        // Here you would implement actual map initialization
    }, 2000);
}

function showNotification(message, type) {
    // Create modern notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 350px;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        backdrop-filter: blur(15px);
        border: none;
        font-weight: 600;
    `;
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 4 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

// Add smooth animations on page load
document.addEventListener('DOMContentLoaded', function() {
    // Animate cards
    const cards = document.querySelectorAll('.modern-tracking-card, .stat-card-modern, .rider-status-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Show welcome notification
    setTimeout(() => {
        showNotification('🚀 Live tracking dashboard loaded successfully!', 'success');
    }, 1000);

    // Load live orders
    loadLiveOrders();
});

// Auto-refresh functionality
let autoRefreshInterval;

function startAutoRefresh() {
    autoRefreshInterval = setInterval(() => {
        showNotification('🔄 Auto-refreshing data...', 'info');
        loadLiveOrders();
    }, 30000);
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        showNotification('⏸️ Auto-refresh stopped', 'info');
    }
}

// Start auto-refresh on page load
document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();
});

// Load Google Maps API key dynamically
fetch('/api/get_google_maps_key')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.enabled && data.api_key) {
            // Load Google Maps API with the retrieved key
            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=${data.api_key}&callback=initMap`;
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
        } else {
            console.warn('Google Maps API key not configured or disabled');
            // Show message to user that maps are not available
            showMapsUnavailableMessage();
        }
    })
    .catch(error => {
        console.error('Error loading Google Maps API key:', error);
        showMapsUnavailableMessage();
    });

function showMapsUnavailableMessage() {
    // Replace map containers with message
    const mapContainers = document.querySelectorAll('.map-container, #map');
    mapContainers.forEach(container => {
        container.innerHTML = `
            <div class="alert alert-warning text-center">
                <h5><i class="fas fa-exclamation-triangle"></i> Google Maps Not Available</h5>
                <p>Google Maps API key is not configured. Please contact administrator.</p>
                <a href="/admin/api_keys" class="btn btn-primary btn-sm">
                    <i class="fas fa-key"></i> Configure API Key
                </a>
            </div>
        `;
    });
}

// Initialize map when Google Maps API is loaded
function initMap() {
    console.log('Google Maps API loaded successfully');
    // Map initialization code can be added here
}
</script>
{% endblock %}
