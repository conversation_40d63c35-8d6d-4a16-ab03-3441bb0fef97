{% extends 'base.html' %}

{% block title %}Order Details - {{ order.order_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Order Details - {{ order.order_id }}</h1>
        <div class="btn-group">
            <a href="{{ url_for('orders.index') }}" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Orders
            </a>
            <!-- Invoice viewing temporarily disabled -->
            <button class="btn btn-sm btn-primary" onclick="alert('Invoice viewing temporarily disabled during maintenance')">
                <i class="fas fa-file-invoice"></i> View Invoice
            </button>
        </div>
    </div>

    <!-- Order Information -->
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <th width="40%">Order ID:</th>
                                    <td><strong>{{ order.order_id }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Customer:</th>
                                    <td>{{ order.customer_name }}</td>
                                </tr>
                                <tr>
                                    <th>Order Date:</th>
                                    <td>{{ order.order_date|format_datetime }}</td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge 
                                            {% if order.status == 'Placed' %}badge-warning
                                            {% elif order.status == 'Approved' %}badge-primary
                                            {% elif order.status == 'Processing' %}badge-info
                                            {% elif order.status == 'Ready for Pickup' %}badge-secondary
                                            {% elif order.status == 'Dispatched' %}badge-dark
                                            {% elif order.status == 'Delivered' %}badge-success
                                            {% elif order.status == 'Cancelled' %}badge-danger
                                            {% endif %}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <th width="40%">Order Amount:</th>
                                    <td><strong>{{ order.order_amount|format_currency }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Payment Method:</th>
                                    <td>{{ order.payment_method or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Sales Agent:</th>
                                    <td>{{ order.sales_agent or 'N/A' }}</td>
                                </tr>
                                {% if order.invoice_number %}
                                <tr>
                                    <th>Invoice Number:</th>
                                    <td><strong>{{ order.invoice_number }}</strong></td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Items</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Strength</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order_items %}
                                <tr>
                                    <td>{{ item.product_name or 'Unknown Product' }}</td>
                                    <td>{{ item.strength or 'N/A' }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.unit_price|format_currency }}</td>
                                    <td>{{ (item.quantity * item.unit_price)|format_currency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Customer Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <th>Name:</th>
                            <td>{{ order.customer_name }}</td>
                        </tr>
                        <tr>
                            <th>Address:</th>
                            <td>
                                {{ order.customer_address or 'N/A' }}
                                {% if order.customer_address %}
                                <br>
                                <button type="button" class="btn btn-sm btn-outline-primary mt-1" onclick="openCustomerAddressOnMap('{{ order.customer_address }}', '{{ order.customer_name }}')">
                                    <i class="fas fa-map-marker-alt"></i> View on Map
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Phone:</th>
                            <td>{{ order.customer_phone or 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>{{ order.customer_email or 'N/A' }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Order Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <!-- Approve Order Button - Only for Placed orders -->
                    {% if order.status == 'Placed' %}
                    <form action="{{ url_for('orders.approve_order', order_id=order.order_id) }}" method="post" class="d-inline w-100">
                        <button type="submit" class="btn btn-success btn-block mb-2" onclick="return confirm('Are you sure you want to approve this order?')">
                            <i class="fas fa-check"></i> Approve Order
                        </button>
                    </form>
                    {% endif %}

                    <!-- Generate Invoice Button - Only for Approved/Processing orders -->
                    {% if order.status in ['Approved', 'Processing'] %}
                    <a href="{{ url_for('finance_generate_invoice') }}"
                       class="btn btn-primary btn-block mb-2">
                        <i class="fas fa-file-invoice"></i> Generate Invoice
                    </a>
                    {% endif %}

                    <!-- Generate DC Button - Only for Approved orders -->
                    {% if order.status == 'Approved' %}
                    <a href="{{ url_for('batch_selection.select_batch', order_id=order.order_id) }}"
                       class="btn btn-warning btn-block mb-2">
                        <i class="fas fa-boxes"></i> Generate DC
                    </a>
                    {% endif %}

                    <!-- Edit Order Button - Only for Placed orders -->
                    {% if order.status == 'Placed' %}
                    <a href="{{ url_for('orders.update_order', order_id=order.order_id) }}"
                       class="btn btn-info btn-block mb-2">
                        <i class="fas fa-edit"></i> Edit Order
                    </a>
                    {% endif %}

                    <!-- View Invoice Button - Only if invoice exists -->
                    {% if order.status in ['Invoiced', 'Completed'] %}
                    <a href="{{ url_for('finance.view_invoice', order_id=order.order_id) }}"
                       class="btn btn-outline-primary btn-block mb-2">
                        <i class="fas fa-file-pdf"></i> View Invoice
                    </a>
                    {% endif %}

                    <!-- Back to Orders Button -->
                    <a href="{{ url_for('orders.index') }}"
                       class="btn btn-secondary btn-block">
                        <i class="fas fa-list"></i> Back to Orders
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Google Maps integration function for customer addresses
function openCustomerAddressOnMap(address, customerName) {
    if (!address || address.trim() === '') {
        alert('No address available for this customer');
        return;
    }

    // Create Google Maps URL with the address
    const encodedAddress = encodeURIComponent(address.trim());
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;

    // Open in new tab
    window.open(googleMapsUrl, '_blank');

    console.log(`Opening Google Maps for ${customerName}: ${address}`);
}
</script>
{% endblock %}
