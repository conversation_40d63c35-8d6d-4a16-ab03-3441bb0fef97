{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-history text-primary"></i> {{ title }}
        </h1>
        <div>
            <a href="{{ url_for('rider_performance') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Performance
            </a>
            <a href="{{ url_for('view_rider_details', rider_id=rider_id) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-user"></i> Rider Details
            </a>
        </div>
    </div>

    <!-- Rider Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-user-circle"></i> {{ rider.name }}
                        <span class="badge badge-light text-primary ml-2">{{ total_deliveries }} Total Deliveries</span>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Phone:</strong> {{ rider.phone or 'N/A' }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Rider ID:</strong> {{ rider_id }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delivery History Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-list"></i> Delivery History
                    </h6>
                </div>
                <div class="card-body">
                    {% if deliveries %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="deliveryHistoryTable">
                            <thead class="thead-light">
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Order Date</th>
                                    <th>Delivery Date</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                    <th>Rating</th>
                                    <th>Payment</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in deliveries %}
                                <tr>
                                    <td>
                                        <strong>{{ delivery.order_id }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ delivery.customer_name }}</strong><br>
                                            <small class="text-muted">{{ delivery.customer_phone or 'N/A' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {{ delivery.order_date|date_only if delivery.order_date else 'N/A' }}
                                    </td>
                                    <td>
                                        {{ delivery.delivery_date|date_only if delivery.delivery_date else 'N/A' }}
                                    </td>
                                    <td>
                                        {% if delivery.status == 'Delivered' %}
                                            <span class="badge badge-success">{{ delivery.status }}</span>
                                        {% elif delivery.status == 'Failed' %}
                                            <span class="badge badge-danger">{{ delivery.status }}</span>
                                        {% elif delivery.status == 'In Transit' %}
                                            <span class="badge badge-warning">{{ delivery.status }}</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{{ delivery.status or 'Unknown' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>₹{{ delivery.order_amount|safe_currency if delivery.order_amount else '0' }}</strong>
                                    </td>
                                    <td>
                                        {% if delivery.delivery_rating %}
                                            <div class="text-warning">
                                                {% for i in range(1, 6) %}
                                                    {% if i <= delivery.delivery_rating %}
                                                        <i class="fas fa-star"></i>
                                                    {% else %}
                                                        <i class="far fa-star"></i>
                                                    {% endif %}
                                                {% endfor %}
                                                <small>({{ delivery.delivery_rating }})</small>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">No rating</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if delivery.payment_status == 'paid' %}
                                            <span class="badge badge-success">Paid</span>
                                        {% elif delivery.payment_status == 'pending' %}
                                            <span class="badge badge-warning">Pending</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{{ delivery.payment_status or 'Unknown' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewOrderDetails('{{ delivery.order_id }}')" title="View Order">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if delivery.delivery_feedback %}
                                            <button class="btn btn-sm btn-outline-info" onclick="showFeedback('{{ delivery.delivery_feedback }}')" title="View Feedback">
                                                <i class="fas fa-comment"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No delivery history found</h5>
                        <p class="text-muted">This rider hasn't completed any deliveries yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feedback Modal -->
<div class="modal fade" id="feedbackModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Customer Feedback</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="feedbackText"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#deliveryHistoryTable').DataTable({
        "order": [[ 2, "desc" ]], // Sort by order date descending
        "pageLength": 25,
        "responsive": true,
        "language": {
            "search": "Search deliveries:",
            "lengthMenu": "Show _MENU_ deliveries per page",
            "info": "Showing _START_ to _END_ of _TOTAL_ deliveries"
        }
    });
});

function viewOrderDetails(orderId) {
    window.location.href = `/orders/${orderId}/view`;
}

function showFeedback(feedback) {
    $('#feedbackText').text(feedback);
    $('#feedbackModal').modal('show');
}
</script>

<style>
.table th {
    background-color: #f8f9fc;
    border-top: none;
}

.badge {
    font-size: 0.75em;
}

.btn-group .btn {
    margin-right: 2px;
}

.fa-star {
    font-size: 0.8em;
}
</style>
{% endblock %}
