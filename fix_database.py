#!/usr/bin/env python3
"""
Fix Database Issues - Create Missing Tables
"""

import sqlite3
import os

def fix_database():
    """Create missing delivery_challans table"""
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create delivery_challans table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS delivery_challans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dc_number TEXT UNIQUE NOT NULL,
                order_id TEXT NOT NULL,
                warehouse_id TEXT,
                customer_name TEXT,
                customer_address TEXT,
                status TEXT DEFAULT 'created',
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                dispatch_date TIMESTAMP,
                delivery_date TIMESTAMP,
                created_by TEXT,
                total_items INTEGER DEFAULT 0,
                total_amount REAL DEFAULT 0,
                batch_details TEXT,
                pdf_path TEXT,
                notes TEXT,
                FOREIGN KEY (order_id) REFERENCES orders(order_id),
                FOREIGN KEY (warehouse_id) REFERENCES warehouses(warehouse_id)
            )
        ''')
        
        # Verify table was created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='delivery_challans'")
        if cursor.fetchone():
            print("✅ delivery_challans table created successfully")
        else:
            print("❌ Failed to create delivery_challans table")
            return False
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing database issues...")
    if fix_database():
        print("✅ Database fixed successfully!")
    else:
        print("❌ Database fix failed!")
