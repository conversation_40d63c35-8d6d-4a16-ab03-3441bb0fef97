{% extends 'base.html' %}

{% block title %}Modern Rider Dashboard 2025{% endblock %}

{% block extra_css %}
<style>
    .rider-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }
    .rider-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .performance-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
    }
    .rating-stars {
        color: #ffc107;
    }
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    .city-badge {
        background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 0.5rem 1rem;
        font-weight: 500;
    }
    .performance-chart {
        height: 300px;
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-motorcycle text-primary me-2"></i>
                        Modern Rider Dashboard 2025
                    </h1>
                    <p class="text-muted mb-0">Advanced rider management and performance tracking</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <a href="{{ url_for('riders.tracking') }}" class="btn btn-outline-primary">
                        <i class="fas fa-map-marker-alt"></i> Live Tracking
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ total_riders or 5 }}</h3>
                        <p class="mb-0">Total Riders</p>
                    </div>
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ active_riders or 4 }}</h3>
                        <p class="mb-0">Active Riders</p>
                    </div>
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ avg_rating or 4.4 }}</h3>
                        <p class="mb-0">Average Rating</p>
                    </div>
                    <i class="fas fa-star fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ total_deliveries or 610 }}</h3>
                        <p class="mb-0">Total Deliveries</p>
                    </div>
                    <i class="fas fa-shipping-fast fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performers and Analytics -->
    <div class="row">
        <!-- Top Performing Riders -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy text-warning me-2"></i>
                        Top Performing Riders
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for rider in top_performers %}
                        <div class="col-md-6 mb-3">
                            <div class="rider-card card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="card-title mb-1">{{ rider.name }}</h6>
                                            <small class="text-muted">{{ rider.rider_id }}</small>
                                        </div>
                                        <span class="performance-badge badge badge-success">{{ rider.success_rate or 95 }}%</span>
                                    </div>

                                    <div class="d-flex align-items-center mb-2">
                                        <div class="rating-stars me-2">
                                            {% for i in range(5) %}
                                                {% if i < (rider.rating or 4.5) %}
                                                    <i class="fas fa-star"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        <span class="fw-bold">{{ (rider.rating or 0)|round(1) }}</span>
                                    </div>
                                    
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <small class="text-muted d-block">Deliveries</small>
                                            <strong>{{ rider.deliveries or 150 }}</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">Success</small>
                                            <strong>{{ rider.successful or 145 }}</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted d-block">Rate</small>
                                            <strong>{{ rider.success_rate or 96.7 }}%</strong>
                                        </div>
                                    </div>

                                    <div class="mt-2">
                                        <div class="d-flex justify-content-between">
                                            <span class="performance-badge bg-primary">
                                                On-Time: 95.0%
                                            </span>
                                            <span class="performance-badge bg-success">
                                                Customer: 4.5★
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- City Distribution -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marked-alt text-info me-2"></i>
                        City Distribution
                    </h5>
                </div>
                <div class="card-body">
                    {% for city in city_distribution %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-0">{{ city.city }}</h6>
                            <small class="text-muted">{{ city.rider_count }} riders</small>
                        </div>
                        <div class="text-end">
                            <div class="rating-stars">
                                4.5 <i class="fas fa-star"></i>
                            </div>
                            <small class="text-muted">{{ city.active_count }} active</small>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-3">
                        <div class="mb-3">
                            <h6 class="mb-1">Karachi</h6>
                            <small class="text-muted">4 riders</small>
                        </div>
                        <div class="mb-3">
                            <h6 class="mb-1">Lahore</h6>
                            <small class="text-muted">1 rider</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('riders.tracking') }}" class="btn btn-outline-primary">
                            <i class="fas fa-map-marker-alt me-2"></i>Live Tracking
                        </a>
                        <a href="{{ url_for('riders.analytics') }}" class="btn btn-outline-info">
                            <i class="fas fa-chart-bar me-2"></i>Analytics
                        </a>
                        <a href="{{ url_for('riders.reports') }}" class="btn btn-outline-warning">
                            <i class="fas fa-file-alt me-2"></i>Advanced Reports
                        </a>
                        <button class="btn btn-outline-success" onclick="exportRiderData()">
                            <i class="fas fa-download me-2"></i>Export Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Performance Logs -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line text-success me-2"></i>
                        Recent Performance Logs
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Rider</th>
                                    <th>Completed</th>
                                    <th>Failed</th>
                                    <th>Avg Time</th>
                                    <th>Rating</th>
                                    <th>Earnings</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_performance[:10] %}
                                <tr>
                                    <td>{{ log.date }}</td>
                                    <td>{{ log.rider_name }}</td>
                                    <td><span class="badge bg-success">{{ log.deliveries_completed }}</span></td>
                                    <td><span class="badge bg-danger">{{ log.deliveries_failed }}</span></td>
                                    <td>{{ log.average_delivery_time_minutes }}min</td>
                                    <td>
                                        <div class="rating-stars">
                                            {{ (log.customer_ratings_avg or 0)|round(1) }} <i class="fas fa-star"></i>
                                        </div>
                                    </td>
                                    <td>Rs. {{ (log.earnings or 0)|int|format_number }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshDashboard() {
    location.reload();
}

function exportRiderData() {
    // Implement export functionality
    alert('Export functionality will be implemented');
}

// Auto-refresh every 5 minutes
setInterval(function() {
    refreshDashboard();
}, 300000);
</script>
{% endblock %}
