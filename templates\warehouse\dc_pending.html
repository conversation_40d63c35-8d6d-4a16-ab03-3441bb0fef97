{% extends "base.html" %}

{% block title %}DC Pending - Batch Selection System{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-truck-loading"></i> DC Pending - Batch Selection System
        </h1>
        <div>
            <a href="{{ url_for('warehouses') }}" class="btn btn-secondary">
                <i class="fas fa-warehouse"></i> Back to Warehouses
            </a>
        </div>
    </div>

    <!-- KPI Cards Row -->
    <div class="row">
        <!-- Pending DCs Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending DCs
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ kpi_data.pending_count }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Today's Completed Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Today's Completed
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ kpi_data.today_completed }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Revenue Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Pending Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ₹{{ "%.2f"|format(kpi_data.pending_revenue) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Orders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Orders
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ kpi_data.total_orders }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Orders Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-primary text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-list"></i> Approved Orders Waiting for DC Generation
            </h6>
        </div>
        <div class="card-body">
            {% if pending_orders %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="pendingOrdersTable">
                    <thead class="thead-light">
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Order Date</th>
                            <th>Approval Date</th>
                            <th>Items</th>
                            <th>Quantity</th>
                            <th>Amount</th>
                            <th>Priority</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in pending_orders %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ order.order_id }}</strong>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ order.customer_name }}</strong>
                                    {% if order.customer_phone %}
                                    <br><small class="text-muted">{{ order.customer_phone }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {{ safe_strftime(order.order_date, '%Y-%m-%d') if order.order_date else 'N/A' }}
                            </td>
                            <td>
                                {% if order.approval_date %}
                                    <span class="text-success">
                                        {{ safe_strftime(order.approval_date, '%Y-%m-%d') }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge badge-info">{{ order.total_items or 0 }}</span>
                            </td>
                            <td>
                                <span class="badge badge-secondary">{{ order.total_quantity or 0 }}</span>
                            </td>
                            <td>
                                <strong class="text-success">₹{{ "%.2f"|format(order.order_amount) }}</strong>
                            </td>
                            <td>
                                <span class="badge badge-info">Normal</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('dc_generation.batch_selection', order_id=order.order_id) }}"
                                       class="btn btn-primary btn-sm"
                                       title="Start Batch Selection">
                                        <i class="fas fa-boxes"></i> Select Batches
                                    </a>
                                    <a href="{{ url_for('view_order', order_id=order.order_id) }}" 
                                       class="btn btn-info btn-sm" 
                                       title="View Order Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5 class="text-muted">No Pending DCs</h5>
                <p class="text-muted">All approved orders have been processed for DC generation.</p>
                <a href="{{ url_for('orders') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> View All Orders
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-info text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-bolt"></i> Quick Actions
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <a href="{{ url_for('delivery_challans') }}" class="btn btn-outline-primary btn-block">
                        <i class="fas fa-truck"></i><br>
                        View All DCs
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('warehouses') }}" class="btn btn-outline-info btn-block">
                        <i class="fas fa-warehouse"></i><br>
                        Warehouse Management
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-success btn-block">
                        <i class="fas fa-boxes"></i><br>
                        Inventory Status
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ url_for('orders') }}" class="btn btn-outline-warning btn-block">
                        <i class="fas fa-shopping-cart"></i><br>
                        All Orders
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable for better sorting and filtering
    $('#pendingOrdersTable').DataTable({
        "order": [[ 3, "desc" ]], // Sort by approval date descending
        "pageLength": 25,
        "responsive": true,
        "columnDefs": [
            { "orderable": false, "targets": 8 } // Disable sorting on Actions column
        ]
    });
    
    // Auto-refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000); // 5 minutes
});

// Function to handle bulk actions (if needed in future)
function selectAllOrders() {
    const checkboxes = document.querySelectorAll('input[name="selected_orders"]');
    const selectAllCheckbox = document.getElementById('selectAll');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}
</script>
{% endblock %}
