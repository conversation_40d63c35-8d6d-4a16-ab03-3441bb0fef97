#!/usr/bin/env python3
"""
Test All Fixes
Comprehensive test script to verify all three issues have been resolved
"""

import sqlite3
import os
import sys
from datetime import datetime

def test_database_tables():
    """Test that all required tables exist"""
    print("🔍 TESTING DATABASE TABLES")
    print("=" * 50)
    
    db_path = 'instance/medivent.db'
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check required tables
        required_tables = ['rider_bikes', 'rider_performance_logs', 'delivery_challans', 'riders']
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        all_exist = True
        for table in required_tables:
            if table in existing_tables:
                print(f"✅ {table} - EXISTS")
            else:
                print(f"❌ {table} - MISSING")
                all_exist = False
        
        # Test data access
        if all_exist:
            print("\n📊 TESTING DATA ACCESS")
            print("-" * 30)
            
            # Test rider_bikes table
            cursor.execute("SELECT COUNT(*) FROM rider_bikes;")
            bike_count = cursor.fetchone()[0]
            print(f"✅ rider_bikes: {bike_count} records")
            
            # Test rider_performance_logs table
            cursor.execute("SELECT COUNT(*) FROM rider_performance_logs;")
            log_count = cursor.fetchone()[0]
            print(f"✅ rider_performance_logs: {log_count} records")
            
            # Test delivery_challans table
            cursor.execute("SELECT COUNT(*) FROM delivery_challans;")
            dc_count = cursor.fetchone()[0]
            print(f"✅ delivery_challans: {dc_count} records")
            
            # Test riders table
            cursor.execute("SELECT COUNT(*) FROM riders;")
            rider_count = cursor.fetchone()[0]
            print(f"✅ riders: {rider_count} records")
        
        conn.close()
        return all_exist
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_template_fixes():
    """Test that template files have been fixed"""
    print("\n🔍 TESTING TEMPLATE FIXES")
    print("=" * 50)
    
    template_files = [
        'templates/delivery_challans/view.html',
        'templates/delivery_challans/index.html'
    ]
    
    all_fixed = True
    
    for template_file in template_files:
        if not os.path.exists(template_file):
            print(f"❌ {template_file} - NOT FOUND")
            all_fixed = False
            continue
        
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for problematic patterns
            problematic_patterns = [
                '.strftime(',
                'order_date.strftime',
                'created_date.strftime',
                'date_generated.strftime'
            ]
            
            issues_found = []
            for pattern in problematic_patterns:
                if pattern in content:
                    issues_found.append(pattern)
            
            if issues_found:
                print(f"❌ {template_file} - ISSUES FOUND: {', '.join(issues_found)}")
                all_fixed = False
            else:
                # Check for safe_strftime usage
                if 'safe_strftime(' in content:
                    print(f"✅ {template_file} - FIXED (uses safe_strftime)")
                else:
                    print(f"⚠️  {template_file} - NO DATE FORMATTING FOUND")
        
        except Exception as e:
            print(f"❌ {template_file} - ERROR READING: {e}")
            all_fixed = False
    
    return all_fixed

def test_app_configuration():
    """Test that the Flask app has safe_strftime configured"""
    print("\n🔍 TESTING APP CONFIGURATION")
    print("=" * 50)
    
    try:
        # Check if app.py has safe_strftime function
        if os.path.exists('app.py'):
            with open('app.py', 'r', encoding='utf-8') as f:
                app_content = f.read()
            
            if 'def safe_strftime(' in app_content:
                print("✅ safe_strftime function - DEFINED")
            else:
                print("❌ safe_strftime function - NOT FOUND")
                return False
            
            if 'app.jinja_env.globals.update(safe_strftime=safe_strftime)' in app_content:
                print("✅ safe_strftime template global - REGISTERED")
            else:
                print("❌ safe_strftime template global - NOT REGISTERED")
                return False
            
            return True
        else:
            print("❌ app.py - NOT FOUND")
            return False
            
    except Exception as e:
        print(f"❌ App configuration test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests and provide summary"""
    print("🧪 COMPREHENSIVE FIX VERIFICATION")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all tests
    db_test = test_database_tables()
    template_test = test_template_fixes()
    app_test = test_app_configuration()
    
    # Summary
    print("\n📋 TEST SUMMARY")
    print("=" * 50)
    
    tests = [
        ("Database Tables", db_test),
        ("Template Fixes", template_test),
        ("App Configuration", app_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Delivery challans strftime error - FIXED")
        print("✅ Missing rider_bikes table - FIXED")
        print("✅ Missing rider_performance_logs table - FIXED")
        print("\n🚀 Your application should now work without these errors!")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
