"""
Order management routes for Medivent Pharmaceuticals Web Portal
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, g
from flask_login import login_required, current_user
from datetime import datetime
import os
import uuid
import sqlite3
import time
from werkzeug.utils import secure_filename
import json

# Import PDF generation utilities
from utils.invoice_generator import generate_pdf_invoice
from utils.challan_generator import generate_pdf_challan

# Import validation utilities
from utils.inventory_validator import get_inventory_validator, get_products_with_inventory_for_forms
from utils.product_validator import get_product_validator
from utils.division_validator import get_division_validator

orders_bp = Blueprint('orders', __name__, url_prefix='/orders')

# Database helper function
def get_db():
    """Get database connection"""
    if 'db' not in g:
        g.db = sqlite3.connect(current_app.config['DATABASE'])
        g.db.row_factory = sqlite3.Row
        g.db.execute("PRAGMA foreign_keys = ON")
    return g.db

def generate_order_id():
    """Generate unique order ID with timestamp and random component"""
    timestamp = str(int(time.time()))
    random_part = uuid.uuid4().hex[:8].upper()
    return f"ORD{timestamp}{random_part}"

def generate_order_item_id():
    """Generate unique order item ID"""
    timestamp = str(int(time.time()))
    random_part = uuid.uuid4().hex[:6].upper()
    return f"OI{timestamp}{random_part}"

# Order status constants
ORDER_STATUSES = [
    "Placed",              # Initial status when order is placed
    "Approved",            # Order has been approved (skipping Pending Approval)
    "Processing",          # Order is being processed
    "Finance Pending",     # DC generated, waiting for invoice generation
    "Ready for Pickup",    # Order is ready for rider pickup
    "Dispatched",          # Order has been dispatched with rider
    "Delivered",           # Order has been delivered to customer
    "Cancelled",           # Order has been cancelled
    "Pending"              # Products not available but order is approved
]

def log_order_activity(order_id, action, details, username):
    """Log order activity to the database"""
    try:
        db = get_db()
        db.execute('''
            INSERT INTO activity_logs (entity_type, action, entity_id, description, user_id, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ("order", action, order_id, details, username, datetime.now()))
        db.commit()
    except Exception as e:
        # If activity_logs table doesn't exist, just skip logging
        pass

def generate_order_id():
    """Generate a unique order ID"""
    import time
    timestamp = str(int(time.time()))
    random_part = uuid.uuid4().hex[:8].upper()
    return f"ORD{timestamp}{random_part}"

def generate_invoice_number():
    """Generate a sequential invoice number"""
    db = get_db()
    last_invoice = db.execute('SELECT invoice_number FROM invoices ORDER BY id DESC LIMIT 1').fetchone()
    if last_invoice:
        try:
            # Extract number part after 'INV'
            num = int(last_invoice['invoice_number'][3:])
            next_num = num + 1
        except ValueError:
            next_num = 1
    else:
        next_num = 1

    # Format with leading zeros (e.g., INV00001)
    return f"INV{next_num:05d}"

def generate_dc_number():
    """Generate a sequential delivery challan number"""
    db = get_db()
    last_challan = db.execute('SELECT dc_number FROM delivery_challans ORDER BY id DESC LIMIT 1').fetchone()
    if last_challan:
        try:
            # Extract number part after 'DC-'
            num = int(last_challan['dc_number'][3:])
            next_num = num + 1
        except ValueError:
            next_num = 1
    else:
        next_num = 1

    # Format with leading zeros (e.g., DC-001)
    return f"DC-{next_num:03d}"

@orders_bp.route('/')
@login_required
def index():
    """Order management main page"""
    db = get_db()
    orders = db.execute('SELECT * FROM orders ORDER BY order_date DESC').fetchall()
    return render_template('orders/index.html', orders=orders, statuses=ORDER_STATUSES)

@orders_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_order():
    """Create a new order"""
    if request.method == 'POST':
        try:
            db = get_db()

            # Get form data
            customer_name = request.form.get('customer_name', '').title()
            customer_address = request.form.get('customer_address', '').title()
            customer_phone = request.form.get('customer_phone', '')
            payment_method = request.form.get('payment_method', '').lower()

            # Generate unique order ID with retry mechanism
            max_retries = 5
            order_id = None

            for attempt in range(max_retries):
                order_id = generate_order_id()

                # Check if order ID already exists
                existing = db.execute('SELECT COUNT(*) as count FROM orders WHERE order_id = ?', (order_id,)).fetchone()
                if existing['count'] == 0:
                    break

                # If we reach here, the ID exists, so wait a bit and try again
                time.sleep(0.1)

            if not order_id:
                flash('Error generating unique order ID. Please try again.', 'danger')
                return redirect(url_for('orders.new_order'))

            # Begin transaction
            db.execute('BEGIN TRANSACTION')

            # Insert order
            db.execute('''
                INSERT INTO orders (
                    order_id, customer_name, customer_address, customer_phone,
                    payment_method, status, sales_agent, updated_by, order_date, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id, customer_name, customer_address, customer_phone,
                payment_method, ORDER_STATUSES[0], current_user.username,
                current_user.username, datetime.now(), datetime.now()
            ))

            # Process order items with real-time inventory deduction
            product_ids = request.form.getlist('product_id[]')
            quantities = request.form.getlist('quantity[]')
            foc_quantities = request.form.getlist('foc_quantity[]')

            total_amount = 0
            inventory_validator = get_inventory_validator(db)
            product_validator = get_product_validator(db)

            for i in range(len(product_ids)):
                if i >= len(quantities):
                    continue

                product_id = product_ids[i]
                try:
                    quantity = int(quantities[i])
                    foc_quantity = int(foc_quantities[i]) if i < len(foc_quantities) else 0
                except (ValueError, IndexError):
                    continue

                # Validate product exists and has valid division
                is_valid, product_info = product_validator.validate_product_exists(product_id)
                if not is_valid or not product_info.get('division_valid', False):
                    flash(f'Product {product_id} is not valid for order placement', 'danger')
                    raise Exception(f'Invalid product: {product_id}')

                # Get product details
                product = db.execute('SELECT * FROM products WHERE product_id = ?', (product_id,)).fetchone()
                if not product:
                    continue

                # Validate and execute real-time inventory deduction
                success, message = inventory_validator.execute_stock_deduction(
                    product_id, quantity, foc_quantity, order_id, current_user.username
                )

                if not success:
                    flash(f'Inventory deduction failed for {product["name"]}: {message}', 'danger')
                    raise Exception(f'Inventory deduction failed: {message}')

                # Calculate line total (only for paid quantity, not FOC)
                unit_price = product['unit_price'] or 0.0
                line_total = unit_price * quantity
                total_amount += line_total

                # Generate unique order item ID
                order_item_id = generate_order_item_id()

                # Insert order item with FOC quantity
                db.execute('''
                    INSERT INTO order_items (
                        order_item_id, order_id, product_id, product_name, strength,
                        quantity, foc_quantity, unit_price, line_total, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    order_item_id, order_id, product_id, product['name'],
                    product['strength'], quantity, foc_quantity, unit_price, line_total, ORDER_STATUSES[0]
                ))

            # Update order total
            db.execute('UPDATE orders SET order_amount = ? WHERE order_id = ?', (total_amount, order_id))

            # Commit transaction
            db.execute('COMMIT')

            # Create notification for new order
            try:
                from app import create_notification
                create_notification(
                    title=f'New Order Placed - {order_id}',
                    message=f'Order {order_id} has been placed by {current_user.username} for customer {customer_name}. Total amount: Rs.{total_amount:,.2f}',
                    type_='order',
                    entity_type='order',
                    entity_id=order_id,
                    action_url=f'/orders/{order_id}',
                    user_id=None  # Broadcast to all users
                )
            except Exception as e:
                print(f"Error creating notification: {e}")

            flash(f'Order {order_id} placed successfully with real-time inventory deduction.', 'success')
            return redirect(url_for('orders.view_order', order_id=order_id))

        except Exception as e:
            # Rollback on error
            try:
                db.execute('ROLLBACK')
            except:
                pass
            flash(f'Error creating order: {str(e)}', 'danger')
            return redirect(url_for('orders.new_order'))

    # GET request - show order form
    db = get_db()

    # Get products with available inventory using real-time service
    try:
        from utils.product_realtime_service import get_products_with_inventory_realtime
        products = get_products_with_inventory_realtime(db)

        if not products:
            flash('No products with available inventory found. Please add inventory first.', 'warning')
            return redirect(url_for('inventory.index'))
    except Exception as e:
        flash(f'Error loading products: {str(e)}', 'danger')
        # Fallback to product validator
        try:
            product_validator = get_product_validator(db)
            products = product_validator.get_products_for_order_placement()
        except:
            products = []

    # Get divisions for filtering using unified manager
    try:
        from utils.unified_division_manager import get_unified_division_manager
        unified_manager = get_unified_division_manager(db)
        divisions = unified_manager.get_active_divisions()
    except Exception as e:
        divisions = []

    return render_template('orders/new.html',
                         products=products,
                         divisions=divisions)

@orders_bp.route('/<order_id>')
@login_required
def view_order(order_id):
    """View order details"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found.', 'danger')
        return redirect(url_for('orders.index'))

    # Get order items
    order_items = db.execute('''
        SELECT oi.*, p.name as product_name
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.product_id
        WHERE oi.order_id = ?
    ''', (order_id,)).fetchall()

    # Get activity logs for this order (if table exists)
    try:
        logs = db.execute('''
            SELECT * FROM activity_logs
            WHERE entity_id = ?
            ORDER BY timestamp
        ''', (order_id,)).fetchall()
    except:
        logs = []

    # Get invoice and challan if they exist
    try:
        invoice = db.execute('SELECT * FROM invoices WHERE order_id = ?', (order_id,)).fetchone()
    except:
        invoice = None

    try:
        challan = db.execute('SELECT * FROM delivery_challans WHERE order_id = ?', (order_id,)).fetchone()
    except:
        challan = None

    return render_template('orders/view.html',
                          order=order,
                          order_items=order_items,
                          logs=logs,
                          invoice=invoice,
                          challan=challan,
                          statuses=ORDER_STATUSES)

@orders_bp.route('/<order_id>/update', methods=['GET', 'POST'])
@login_required
def update_order(order_id):
    """Update an existing order"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found', 'danger')
        return redirect(url_for('orders.index'))

    if request.method == 'POST':
        # Update order details
        customer_name = request.form.get('customer_name', '').title()
        customer_address = request.form.get('customer_address', '').title()
        customer_phone = request.form.get('customer_phone', '')
        payment_method = request.form.get('payment_method', '').lower()
        notes = request.form.get('notes', '')

        # Update status if provided
        new_status = request.form.get('status')
        if new_status and new_status in ORDER_STATUSES:
            old_status = order['status']

            # Update specific date fields based on status
            approval_date = order['approval_date']
            approved_by = order['approved_by']
            dispatch_date = order['dispatch_date']

            if new_status == "Approved" and old_status != "Approved":
                approval_date = datetime.utcnow().isoformat()
                approved_by = current_user.username
            elif new_status == "Dispatched" and old_status != "Dispatched":
                dispatch_date = datetime.utcnow().isoformat()
        else:
            new_status = order['status']
            approval_date = order['approval_date']
            approved_by = order['approved_by']
            dispatch_date = order['dispatch_date']

        try:
            # Update order in database
            db.execute('''
                UPDATE orders
                SET customer_name = ?, customer_address = ?, customer_phone = ?,
                    payment_method = ?, notes = ?, status = ?,
                    approval_date = ?, approved_by = ?, dispatch_date = ?,
                    updated_by = ?
                WHERE order_id = ?
            ''', (customer_name, customer_address, customer_phone, payment_method,
                  notes, new_status, approval_date, approved_by, dispatch_date,
                  current_user.username, order_id))

            # Log the activity
            db.execute('''
                INSERT INTO activity_logs (user_id, action, details, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (current_user.id, f"Updated order {order_id}",
                  f"Order details updated by {current_user.username}",
                  datetime.utcnow().isoformat()))

            db.commit()
            flash('Order updated successfully!', 'success')
            return redirect(url_for('orders.view_order', order_id=order_id))

        except Exception as e:
            db.rollback()
            flash(f'Error updating order: {str(e)}', 'danger')
            return redirect(url_for('orders.view_order', order_id=order_id))

    # GET request - show update form
    products = db.execute('SELECT * FROM products ORDER BY name').fetchall()
    return render_template('orders/update.html',
                          order=order,
                          products=products,
                          statuses=ORDER_STATUSES)

# TEMPORARILY DISABLED - NEEDS CONVERSION TO RAW SQLITE
# @orders_bp.route('/workflow')
# @login_required
# def workflow():
#     """Order workflow management - DISABLED UNTIL CONVERTED TO RAW SQLITE"""
#     flash('Workflow functionality is temporarily disabled.', 'warning')
#     return redirect(url_for('orders.index'))

@orders_bp.route('/<order_id>/approve', methods=['POST'])
@login_required
def approve_order(order_id):
    """Approve an order and generate invoice"""
    order = Order.query.filter_by(order_id=order_id).first_or_404()

    if order.status not in ["Placed", "Pending"]:
        flash('Only orders with status "Placed" or "Pending" can be approved.', 'warning')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # Generate invoice number
    invoice_number = generate_invoice_number()

    # Update order status and set invoice number
    order.status = "Approved"
    order.invoice_number = invoice_number
    order.approval_date = datetime.utcnow()
    order.approved_by = current_user.username
    order.updated_by = current_user.username

    # Update all order items status
    order_items = OrderItem.query.filter_by(order_id=order_id).all()

    # Check inventory for each item
    available_items = []
    unavailable_items = []

    for item in order_items:
        # Check if product is available in inventory
        inventory = Inventory.query.filter_by(product_id=item.product_id).filter(Inventory.stock_quantity >= item.quantity).first()

        if inventory:
            item.status = "Approved"
            available_items.append(item)
        else:
            item.status = "Pending"
            unavailable_items.append(item)

    # Generate PDF invoice
    invoice_path = generate_pdf_invoice(order, invoice_number, current_user.username)

    # Save invoice record
    invoice = Invoice(
        invoice_number=invoice_number,
        order_id=order_id,
        generated_by=current_user.username,
        pdf_path=invoice_path
    )
    db.session.add(invoice)

    # Generate delivery challan if there are available items
    if available_items:
        dc_number = generate_dc_number()
        challan_path = generate_pdf_challan(order, invoice_number, dc_number, current_user.username, available_items, unavailable_items)

        # Save challan record
        challan = Challan(
            dc_number=dc_number,
            invoice_number=invoice_number,
            order_id=order_id,
            generated_by=current_user.username,
            pdf_path=challan_path
        )
        db.session.add(challan)

    # Commit changes
    db.session.commit()

    # Log activity
    log_order_activity(
        order_id,
        "Order Approved",
        f"Order approved by {current_user.username}. Invoice: {invoice_number}",
        current_user.username
    )

    # Create notification for order approval
    try:
        from app import create_notification
        create_notification(
            title=f'Order Approved - {order_id}',
            message=f'Order {order_id} has been approved by {current_user.username}. Invoice #{invoice_number} generated.',
            type_='success',
            entity_type='order',
            entity_id=order_id,
            action_url=f'/orders/{order_id}',
            user_id=None  # Broadcast to all users
        )
    except Exception as e:
        print(f"Error creating approval notification: {e}")

    flash(f'Order approved successfully. Invoice #{invoice_number} generated.', 'success')
    return redirect(url_for('orders.view_order', order_id=order_id))

@orders_bp.route('/<order_id>/dispatch', methods=['POST'])
@login_required
def dispatch_order(order_id):
    """Dispatch an order"""
    order = Order.query.filter_by(order_id=order_id).first_or_404()

    if order.status not in ["Approved", "Processing", "Ready for Pickup"]:
        flash('Only approved orders can be dispatched.', 'warning')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # Assign rider if provided
    rider = request.form.get('rider')
    if rider:
        order.rider_id = rider

    # Update order status
    order.status = "Dispatched"
    order.dispatch_date = datetime.utcnow()
    order.updated_by = current_user.username

    # Update inventory for available items
    order_items = OrderItem.query.filter_by(order_id=order_id, status="Approved").all()

    for item in order_items:
        # Find inventory with sufficient quantity
        inventory = Inventory.query.filter_by(product_id=item.product_id).filter(Inventory.stock_quantity >= item.quantity).first()

        if inventory:
            # Deduct from inventory
            inventory.stock_quantity -= item.quantity
            inventory.last_updated = datetime.utcnow()
            inventory.updated_by = current_user.username

            # Update item status
            item.status = "Dispatched"

    # Commit changes
    db.session.commit()

    # Log activity
    log_order_activity(
        order_id,
        "Order Dispatched",
        f"Order dispatched by {current_user.username}. Rider: {order.rider_id}",
        current_user.username
    )

    # Create notification for order dispatch
    try:
        from app import create_notification
        rider_info = f" Assigned to rider: {order.rider_id}" if order.rider_id else ""
        create_notification(
            title=f'Order Dispatched - {order_id}',
            message=f'Order {order_id} has been dispatched by {current_user.username}.{rider_info}',
            type_='info',
            entity_type='order',
            entity_id=order_id,
            action_url=f'/orders/{order_id}',
            user_id=None  # Broadcast to all users
        )
    except Exception as e:
        print(f"Error creating dispatch notification: {e}")

    flash('Order dispatched successfully.', 'success')
    return redirect(url_for('orders.view_order', order_id=order_id))

@orders_bp.route('/<order_id>/deliver', methods=['POST'])
@login_required
def deliver_order(order_id):
    """Mark an order as delivered"""
    order = Order.query.filter_by(order_id=order_id).first_or_404()

    if order.status != "Dispatched":
        flash('Only dispatched orders can be marked as delivered.', 'warning')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # Update order status
    order.status = "Delivered"
    order.delivery_date = datetime.utcnow()
    order.updated_by = current_user.username

    # Update all dispatched items
    order_items = OrderItem.query.filter_by(order_id=order_id, status="Dispatched").all()
    for item in order_items:
        item.status = "Delivered"

    # Commit changes
    db.session.commit()

    # Log activity
    log_order_activity(
        order_id,
        "Order Delivered",
        f"Order marked as delivered by {current_user.username}",
        current_user.username
    )

    # Create notification for order delivery
    try:
        from app import create_notification
        create_notification(
            title=f'Order Delivered - {order_id}',
            message=f'Order {order_id} has been successfully delivered. Marked by {current_user.username}.',
            type_='success',
            entity_type='order',
            entity_id=order_id,
            action_url=f'/orders/{order_id}',
            user_id=None  # Broadcast to all users
        )
    except Exception as e:
        print(f"Error creating delivery notification: {e}")

    flash('Order marked as delivered successfully.', 'success')
    return redirect(url_for('orders.view_order', order_id=order_id))

@orders_bp.route('/<order_id>/cancel', methods=['POST'])
@login_required
def cancel_order(order_id):
    """Cancel an order"""
    order = Order.query.filter_by(order_id=order_id).first_or_404()

    if order.status in ["Delivered", "Cancelled"]:
        flash('This order cannot be cancelled.', 'warning')
        return redirect(url_for('orders.view_order', order_id=order_id))

    # Update order status
    order.status = "Cancelled"
    order.updated_by = current_user.username

    # Update all order items
    order_items = OrderItem.query.filter_by(order_id=order_id).all()
    for item in order_items:
        item.status = "Cancelled"

    # Commit changes
    db.session.commit()

    # Log activity
    log_order_activity(
        order_id,
        "Order Cancelled",
        f"Order cancelled by {current_user.username}",
        current_user.username
    )

    flash('Order cancelled successfully.', 'success')
    return redirect(url_for('orders.view_order', order_id=order_id))

@orders_bp.route('/search')
@login_required
def search():
    """Search orders"""
    query = request.args.get('q', '')

    if not query:
        return redirect(url_for('orders.index'))

    # Search by order ID, customer name, or phone
    orders = Order.query.filter(
        (Order.order_id.ilike(f'%{query}%')) |
        (Order.customer_name.ilike(f'%{query}%')) |
        (Order.customer_phone.ilike(f'%{query}%')) |
        (Order.invoice_number.ilike(f'%{query}%'))
    ).order_by(desc(Order.order_date)).all()

    return render_template('orders/search_results.html', orders=orders, query=query)

@orders_bp.route('/<order_id>/history')
@login_required
def view_history(order_id):
    """View order history/activity logs"""
    order = Order.query.filter_by(order_id=order_id).first_or_404()
    logs = ActivityLog.query.filter_by(entity_id=order_id).order_by(ActivityLog.timestamp).all()

    return render_template('orders/history.html', order=order, logs=logs)

@orders_bp.route('/<order_id>/invoice')
@login_required
def view_invoice(order_id):
    """View order invoice"""
    invoice = Invoice.query.filter_by(order_id=order_id).first_or_404()

    # If PDF exists, serve it
    if invoice.pdf_path and os.path.exists(invoice.pdf_path):
        return redirect(url_for('static', filename=f'documents/invoices/{os.path.basename(invoice.pdf_path)}'))

    # Otherwise, show invoice details
    order = Order.query.filter_by(order_id=order_id).first_or_404()
    order_items = OrderItem.query.filter_by(order_id=order_id).all()

    return render_template('orders/invoice.html', order=order, order_items=order_items, invoice=invoice)

@orders_bp.route('/<order_id>/challan')
@login_required
def view_challan(order_id):
    """View delivery challan"""
    challan = Challan.query.filter_by(order_id=order_id).first_or_404()

    # If PDF exists, serve it
    if challan.pdf_path and os.path.exists(challan.pdf_path):
        return redirect(url_for('static', filename=f'documents/challans/{os.path.basename(challan.pdf_path)}'))

    # Otherwise, show challan details
    order = Order.query.filter_by(order_id=order_id).first_or_404()
    order_items = OrderItem.query.filter_by(order_id=order_id).all()

    return render_template('orders/challan.html', order=order, order_items=order_items, challan=challan)
