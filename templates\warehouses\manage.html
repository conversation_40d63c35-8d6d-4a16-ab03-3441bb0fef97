{% extends 'base.html' %}

{% block title %}Manage Warehouses - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
<style>
    .main-content {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin: 20px;
        padding: 30px;
    }
    .warehouse-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    .warehouse-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    .warehouse-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 15px 15px 0 0;
    }
    .warehouse-placeholder {
        width: 100%;
        height: 200px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 15px 15px 0 0;
    }
    .status-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
    }
    .btn-action {
        margin: 2px;
        border-radius: 8px;
    }
    .stats-card {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="main-content">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="text-primary mb-1">
                    <i class="fas fa-warehouse"></i> Manage Warehouses
                </h2>
                <p class="text-muted mb-0">View and manage all warehouse locations</p>
            </div>
            <div>
                <a href="/warehouse-management/add" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Warehouse
                </a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ warehouses|length }}</h3>
                            <p class="mb-0">Total Warehouses</p>
                        </div>
                        <i class="fas fa-warehouse fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ warehouses|selectattr('status', 'equalto', 'active')|list|length }}</h3>
                            <p class="mb-0">Active Warehouses</p>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ warehouses|selectattr('status', 'equalto', 'inactive')|list|length }}</h3>
                            <p class="mb-0">Inactive Warehouses</p>
                        </div>
                        <i class="fas fa-pause-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ warehouses|selectattr('capacity')|sum(attribute='capacity')|default(0, true) }}</h3>
                            <p class="mb-0">Total Capacity (sq ft)</p>
                        </div>
                        <i class="fas fa-expand-arrows-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Warehouses Grid -->
        {% if warehouses %}
        <div class="row">
            {% for warehouse in warehouses %}
            <div class="col-md-6 col-lg-4">
                <div class="warehouse-card card position-relative">
                    <!-- Status Badge -->
                    {% if warehouse.status == 'active' %}
                    <span class="badge badge-success status-badge">Active</span>
                    {% else %}
                    <span class="badge badge-warning status-badge">Inactive</span>
                    {% endif %}

                    <!-- Warehouse Image -->
                    {% if warehouse.image_path %}
                    <img src="{{ url_for('static', filename=warehouse.image_path) }}" 
                         alt="{{ warehouse.name }}" class="warehouse-image">
                    {% else %}
                    <div class="warehouse-placeholder">
                        <i class="fas fa-warehouse fa-4x text-muted"></i>
                    </div>
                    {% endif %}

                    <div class="card-body">
                        <h5 class="card-title text-primary">{{ warehouse.name }}</h5>
                        
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt"></i> 
                                {{ warehouse.city }}{% if warehouse.country %}, {{ warehouse.country }}{% endif %}
                            </small>
                        </div>

                        {% if warehouse.address %}
                        <p class="card-text text-muted small">{{ warehouse.address[:100] }}{% if warehouse.address|length > 100 %}...{% endif %}</p>
                        {% endif %}

                        <div class="row text-center mb-3">
                            {% if warehouse.capacity %}
                            <div class="col-4">
                                <small class="text-muted">Capacity</small>
                                <div class="font-weight-bold">{{ warehouse.capacity }} sq ft</div>
                            </div>
                            {% endif %}
                            {% if warehouse.manager %}
                            <div class="col-4">
                                <small class="text-muted">Manager</small>
                                <div class="font-weight-bold small">{{ warehouse.manager }}</div>
                            </div>
                            {% endif %}
                            {% if warehouse.phone %}
                            <div class="col-4">
                                <small class="text-muted">Phone</small>
                                <div class="font-weight-bold small">{{ warehouse.phone }}</div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Action Buttons -->
                        <div class="text-center">
                            <a href="/warehouse-management/edit/{{ warehouse.warehouse_id }}"
                               class="btn btn-sm btn-outline-primary btn-action">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger btn-action" 
                                    onclick="confirmDelete('{{ warehouse.warehouse_id }}', '{{ warehouse.name }}')">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                            <a href="#" class="btn btn-sm btn-outline-info btn-action">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-5">
            <i class="fas fa-warehouse fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">No Warehouses Found</h4>
            <p class="text-muted">Start by adding your first warehouse location</p>
            <a href="/warehouse-management/add" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add First Warehouse
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete warehouse <strong id="warehouse-name"></strong>?</p>
                <p class="text-danger small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="delete-form" method="post" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete Warehouse</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(warehouseId, warehouseName) {
    document.getElementById('warehouse-name').textContent = warehouseName;
    document.getElementById('delete-form').action = `/warehouse-management/delete/${warehouseId}`;
    $('#deleteModal').modal('show');
}
</script>
{% endblock %}
