{% extends "base.html" %}

{% block title %}Payment Collection - Medivent ERP{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
{% endblock %}

{% block content %}
<style>
    /* Enhanced Payment Collection Styles - Matching Reference Image */
    .payment-dashboard {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* Header Section */
    .payment-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .payment-title {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .payment-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        margin-bottom: 0;
    }

    /* Analytics Cards */
    .analytics-cards {
        margin-bottom: 25px;
    }

    .analytics-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        height: 120px;
        display: flex;
        align-items: center;
    }

    .analytics-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    }

    .analytics-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-right: 20px;
    }

    .analytics-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .analytics-content p {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin: 0;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .filter-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .filter-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .filter-row {
        margin-bottom: 15px;
    }

    .filter-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 5px;
        display: block;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .btn-filter {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }

    .btn-process {
        background: linear-gradient(135deg, #27ae60, #229954);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 8px 20px;
        font-weight: 600;
        margin-left: 10px;
        transition: all 0.3s ease;
    }

    .btn-process:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        color: white;
    }
    
    /* Chart Section */
    .chart-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .chart-title {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .chart-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }

    /* Payment Table */
    .payment-table {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .table-title i {
        margin-right: 10px;
        color: #3498db;
    }

    .table-modern {
        border: none;
        border-radius: 10px;
        overflow: hidden;
    }

    .table-modern thead {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
    }

    .table-modern thead th {
        border: none;
        padding: 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .table-modern tbody td {
        border: none;
        padding: 12px 15px;
        vertical-align: middle;
        border-bottom: 1px solid #ecf0f1;
    }

    .table-modern tbody tr:hover {
        background: #f8f9fa;
    }

    .status-badge {
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-pending {
        background: #fef9e7;
        color: #f39c12;
        border: 1px solid #f39c12;
    }

    .status-paid {
        background: #d5f4e6;
        color: #27ae60;
        border: 1px solid #27ae60;
    }

    .status-overdue {
        background: #fadbd8;
        color: #e74c3c;
        border: 1px solid #e74c3c;
    }

    .priority-badge {
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.7rem;
        font-weight: 500;
    }

    .priority-high {
        background: #fadbd8;
        color: #e74c3c;
    }

    .priority-medium {
        background: #fef9e7;
        color: #f39c12;
    }

    .priority-low {
        background: #d5f4e6;
        color: #27ae60;
    }

    .btn-action {
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        margin: 0 2px;
        transition: all 0.3s ease;
        border: none;
    }

    .btn-collect {
        background: #27ae60;
        color: white;
    }

    .btn-collect:hover {
        background: #229954;
        transform: translateY(-1px);
        color: white;
    }

    .btn-view {
        background: #3498db;
        color: white;
    }

    .btn-view:hover {
        background: #2980b9;
        transform: translateY(-1px);
        color: white;
    }

    .btn-remind {
        background: #f39c12;
        color: white;
    }

    .btn-remind:hover {
        background: #e67e22;
        transform: translateY(-1px);
        color: white;
    }

    /* Modal Styles */
    .payment-modal .modal-content {
        border-radius: 15px;
        border: none;
    }
    
    .payment-modal .modal-header {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .payment-title {
            font-size: 1.5rem;
        }
        
        .analytics-card {
            height: auto;
            padding: 15px;
        }
        
        .analytics-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }
        
        .analytics-content h3 {
            font-size: 1.4rem;
        }
        
        .chart-container {
            height: 250px;
        }
        
        .table-modern {
            font-size: 0.85rem;
        }
        
        .btn-action {
            padding: 4px 8px;
            font-size: 0.75rem;
        }
    }
</style>

<div class="payment-dashboard">
    <div class="dashboard-container">
        <!-- Header -->
        <div class="payment-header">
            <h1 class="payment-title">
                <i class="fas fa-credit-card mr-3"></i>Payment Collection
            </h1>
            <p class="payment-subtitle">Manage and process payments with advanced analytics</p>
        </div>

        <!-- Analytics Cards -->
        <div class="row analytics-cards">
            <div class="col-lg-3 col-md-6">
                <div class="analytics-card">
                    <div class="analytics-icon" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="analytics-content">
                        <h3 id="totalCollected">Rs.0</h3>
                        <p>Total Collected</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="analytics-card">
                    <div class="analytics-icon" style="background: linear-gradient(135deg, #f39c12, #f1c40f);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="analytics-content">
                        <h3 id="pendingPayments">Rs.0</h3>
                        <p>Pending Payments</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="analytics-card">
                    <div class="analytics-icon" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="analytics-content">
                        <h3 id="overduePayments">Rs.0</h3>
                        <p>Overdue Payments</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="analytics-card">
                    <div class="analytics-icon" style="background: linear-gradient(135deg, #3498db, #5dade2);">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="analytics-content">
                        <h3 id="collectionRate">0%</h3>
                        <p>Collection Rate</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="fas fa-filter"></i>Payment Filters
            </div>
            <form id="paymentFilters">
                <div class="row">
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Date Range</label>
                            <select class="form-select" id="dateRange">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Payment Status</label>
                            <select class="form-select" id="paymentStatus">
                                <option value="all">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="paid">Paid</option>
                                <option value="overdue">Overdue</option>
                                <option value="partial">Partial</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">Customer</label>
                            <select class="form-select" id="customerFilter">
                                <option value="all">All Customers</option>
                                <!-- Customer options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="filter-row">
                            <label class="filter-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-filter" onclick="filterPayments()">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <button type="button" class="btn btn-process" onclick="processPayments()">
                                    <i class="fas fa-credit-card"></i> Process
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Charts Section -->
        <div class="row">
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-line"></i>Payment Trends
                    </div>
                    <div class="chart-container">
                        <canvas id="paymentTrendChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-section">
                    <div class="chart-title">
                        <i class="fas fa-chart-pie"></i>Payment Methods
                    </div>
                    <div class="chart-container">
                        <canvas id="paymentMethodChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Table -->
        <div class="payment-table">
            <div class="table-title">
                <div>
                    <i class="fas fa-list"></i>Payment Collection List
                </div>
                <div>
                    <button class="btn btn-process" onclick="showPaymentModal()">
                        <i class="fas fa-plus"></i> New Payment
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-modern">
                    <thead>
                        <tr>
                            <th>Customer</th>
                            <th>Invoice</th>
                            <th>Amount</th>
                            <th>Due Date</th>
                            <th>Status</th>
                            <th>Priority</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="paymentTableBody">
                        {% if payments and payments|length > 0 %}
                            {% for payment in payments %}
                            <tr>
                                <td>
                                    <strong>{{ payment.customer_name }}</strong><br>
                                    {% if payment.customer_phone %}
                                    <small class="text-muted">Contact: {{ payment.customer_phone }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ payment.order_id }}</td>
                                <td>Rs.{{ payment.order_amount|format_currency }}</td>
                                <td>{{ safe_strftime(payment.order_date, '%Y-%m-%d') if payment.order_date else 'N/A' }}</td>
                                <td>
                                    {% if payment.payment_status == 'pending' %}
                                    <span class="status-badge status-pending">Pending</span>
                                    {% elif payment.payment_status == 'paid' %}
                                    <span class="status-badge status-paid">Paid</span>
                                    {% elif payment.payment_status == 'overdue' %}
                                    <span class="status-badge status-overdue">Overdue</span>
                                    {% else %}
                                    <span class="status-badge status-pending">{{ payment.payment_status|title }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.priority_level == 'high' %}
                                    <span class="priority-badge priority-high">High</span>
                                    {% elif payment.priority_level == 'medium' %}
                                    <span class="priority-badge priority-medium">Medium</span>
                                    {% else %}
                                    <span class="priority-badge priority-low">Low</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.payment_status != 'paid' %}
                                    <button class="btn btn-action btn-collect" onclick="collectPayment('{{ payment.order_id }}')">
                                        <i class="fas fa-credit-card"></i> Collect
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-action btn-view" onclick="viewPayment('{{ payment.order_id }}')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    {% if payment.payment_status == 'overdue' %}
                                    <button class="btn btn-action btn-remind" onclick="sendReminder('{{ payment.order_id }}')">
                                        <i class="fas fa-bell"></i> Remind
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="7" class="text-center text-muted py-4">
                                    <i class="fas fa-credit-card fa-3x mb-3 d-block"></i>
                                    <h5>No Payments Found</h5>
                                    <p>No payment records available. Payments will appear here once invoices are generated.</p>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Payment Collection Modal -->
<div class="modal fade payment-modal" id="paymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card me-2"></i>Process Payment
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="paymentForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Customer</label>
                                <input type="text" class="form-control" id="paymentCustomer" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Invoice Number</label>
                                <input type="text" class="form-control" id="paymentInvoice" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Outstanding Amount</label>
                                <input type="text" class="form-control" id="outstandingAmount" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Payment Amount</label>
                                <input type="number" class="form-control" id="paymentAmount" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Payment Method</label>
                                <select class="form-select" id="paymentMethod" required>
                                    <option value="">Select Method</option>
                                    <option value="cash">Cash</option>
                                    <option value="card">Credit/Debit Card</option>
                                    <option value="upi">UPI</option>
                                    <option value="cheque">Cheque</option>
                                    <option value="bank_transfer">Bank Transfer</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Payment Date</label>
                                <input type="date" class="form-control" id="paymentDate" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Reference Number</label>
                        <input type="text" class="form-control" id="referenceNumber" placeholder="Transaction/Cheque/Reference Number">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control" id="paymentNotes" rows="3" placeholder="Additional notes..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-process" onclick="submitPayment()">
                    <i class="fas fa-save"></i> Process Payment
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize charts and functionality
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    document.getElementById('paymentDate').value = new Date().toISOString().split('T')[0];

    // Load initial data
    loadPaymentData();
    initializeCharts();
});

function loadPaymentData() {
    // Load data from database
    document.getElementById('totalCollected').textContent = 'Rs.0';
    document.getElementById('pendingPayments').textContent = 'Rs.0';
    document.getElementById('overduePayments').textContent = 'Rs.0';
    document.getElementById('collectionRate').textContent = '0%';
}

function initializeCharts() {
    // Payment Trend Chart
    const trendCtx = document.getElementById('paymentTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Collections',
                data: [200000, 250000, 180000, 320000, 280000, 350000],
                borderColor: '#27ae60',
                backgroundColor: 'rgba(39, 174, 96, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Pending',
                data: [150000, 120000, 200000, 100000, 140000, 90000],
                borderColor: '#f39c12',
                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });

    // Payment Method Chart
    const methodCtx = document.getElementById('paymentMethodChart').getContext('2d');
    new Chart(methodCtx, {
        type: 'doughnut',
        data: {
            labels: ['Cash', 'Card', 'UPI', 'Bank Transfer', 'Cheque'],
            datasets: [{
                data: [35, 25, 20, 15, 5],
                backgroundColor: [
                    '#27ae60',
                    '#3498db',
                    '#9b59b6',
                    '#e67e22',
                    '#95a5a6'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function filterPayments() {
    const dateRange = document.getElementById('dateRange').value;
    const status = document.getElementById('paymentStatus').value;
    const customer = document.getElementById('customerFilter').value;

    console.log('Filtering payments:', { dateRange, status, customer });

    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Filtering...';
    btn.disabled = true;

    // Simulate filtering
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        alert('Payments filtered successfully!');
    }, 1500);
}

function processPayments() {
    alert('Bulk payment processing feature coming soon!');
}

function showPaymentModal() {
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

function collectPayment(orderId) {
    // Populate modal with order data
    document.getElementById('paymentCustomer').value = '';
    document.getElementById('paymentInvoice').value = orderId;
    document.getElementById('outstandingAmount').value = 'Rs.25,000';
    document.getElementById('paymentAmount').value = '25000';

    showPaymentModal();
}

function viewPayment(orderId) {
    alert(`Viewing payment details for ${orderId}`);
}

function sendReminder(orderId) {
    alert(`Sending payment reminder for ${orderId}`);
}

function submitPayment() {
    const form = document.getElementById('paymentForm');
    if (form.checkValidity()) {
        // Show loading state
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        btn.disabled = true;

        // Simulate payment processing
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('Payment processed successfully!');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
            modal.hide();

            // Reset form
            form.reset();
        }, 2000);
    } else {
        form.reportValidity();
    }
}
</script>

{% endblock %}
