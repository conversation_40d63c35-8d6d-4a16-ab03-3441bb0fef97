{% extends "base.html" %}

{% block title %}DC {{ dc.dc_number }} - View{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-file-alt"></i> Delivery Challan {{ dc.dc_number }}
        </h1>
        <div>
            <a href="{{ url_for('dc_generation.download_dc_pdf', dc_number=dc.dc_number) }}" 
               class="btn btn-success">
                <i class="fas fa-download"></i> Download PDF
            </a>
            <a href="{{ url_for('dc_generation.list_dcs') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <!-- DC Information -->
    <div class="row">
        <div class="col-lg-8">
            <!-- DC Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">Delivery Challan Details</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>DC Number:</strong></td>
                                    <td class="text-primary">{{ dc.dc_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Order ID:</strong></td>
                                    <td>
                                        <a href="{{ url_for('view_order', order_id=dc.order_id) }}">
                                            {{ dc.order_id }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Customer:</strong></td>
                                    <td>{{ dc.customer_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Generated Date:</strong></td>
                                    <td>{{ dc.created_date }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Created By:</strong></td>
                                    <td>{{ dc.created_by or 'System' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        {% if dc.status == 'created' %}
                                            <span class="badge badge-warning">Created</span>
                                        {% elif dc.status == 'dispatched' %}
                                            <span class="badge badge-info">Dispatched</span>
                                        {% elif dc.status == 'delivered' %}
                                            <span class="badge badge-success">Delivered</span>
                                        {% else %}
                                            <span class="badge badge-secondary">{{ dc.status|title }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Total Items:</strong></td>
                                    <td><span class="badge badge-info">{{ dc.total_items }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Total Amount:</strong></td>
                                    <td class="text-success"><strong>₹{{ "%.2f"|format(dc.total_amount) }}</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Customer Address:</strong></td>
                                    <td>{{ dc.customer_address or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Customer Phone:</strong></td>
                                    <td>{{ dc.customer_phone or 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Batch Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-info text-white">
                    <h6 class="m-0 font-weight-bold">Batch Allocation Details</h6>
                </div>
                <div class="card-body">
                    {% if batch_details %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th>Product</th>
                                    <th>Batch Number</th>
                                    <th>Warehouse</th>
                                    <th>Mfg Date</th>
                                    <th>Exp Date</th>
                                    <th>Allocated Qty</th>
                                    <th>Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for batch in batch_details %}
                                <tr>
                                    <td>
                                        <strong>{{ batch.product_name }}</strong>
                                        {% if batch.strength %}
                                            <br><small class="text-muted">{{ batch.strength }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-monospace">{{ batch.batch_number }}</span>
                                    </td>
                                    <td>{{ batch.warehouse_name }}</td>
                                    <td>
                                        {% if batch.manufacturing_date %}
                                            {{ batch.manufacturing_date }}
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if batch.expiry_date %}
                                            {{ batch.expiry_date }}
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ batch.allocated_quantity }}</span>
                                    </td>
                                    <td>{{ batch.location_code or 'N/A' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        No batch allocation details available.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Status Update -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-warning text-white">
                    <h6 class="m-0 font-weight-bold">Update Status</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('update_delivery_challan_status', dc_number=dc.dc_number) }}">
                        <div class="form-group">
                            <label for="status">Status:</label>
                            <select name="status" id="status" class="form-control">
                                <option value="created" {% if dc.status == 'created' %}selected{% endif %}>Created</option>
                                <option value="dispatched" {% if dc.status == 'dispatched' %}selected{% endif %}>Dispatched</option>
                                <option value="delivered" {% if dc.status == 'delivered' %}selected{% endif %}>Delivered</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-save"></i> Update Status
                        </button>
                    </form>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-success text-white">
                    <h6 class="m-0 font-weight-bold">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('view_order', order_id=dc.order_id) }}" 
                           class="btn btn-outline-primary btn-block mb-2">
                            <i class="fas fa-shopping-cart"></i> View Order
                        </a>
                        <a href="{{ url_for('dc_generation.download_dc_pdf', dc_number=dc.dc_number) }}" 
                           class="btn btn-outline-success btn-block mb-2">
                            <i class="fas fa-file-pdf"></i> Download PDF
                        </a>
                        <a href="{{ url_for('dc_generation.list_dcs') }}" 
                           class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-list"></i> All DCs
                        </a>
                    </div>
                </div>
            </div>

            <!-- Summary -->
            <div class="card shadow">
                <div class="card-header py-3 bg-secondary text-white">
                    <h6 class="m-0 font-weight-bold">Summary</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h4 class="text-primary">{{ dc.dc_number }}</h4>
                        <p class="text-muted">{{ dc.total_items }} items</p>
                        <h5 class="text-success">₹{{ "%.2f"|format(dc.total_amount) }}</h5>
                        <small class="text-muted">Generated on {{ dc.created_date }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
