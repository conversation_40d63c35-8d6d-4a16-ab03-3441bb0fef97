#!/usr/bin/env python3
"""
Notification System Module
Comprehensive notification management for the Medivent ERP system
"""

import sqlite3
import json
from datetime import datetime, timedelta
from enum import Enum
from typing import List, Dict, Optional, Any
from flask import current_app, g

class NotificationPriority(Enum):
    """Notification priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class NotificationType(Enum):
    """Notification types"""
    ORDER_PLACED = "order_placed"
    ORDER_APPROVED = "order_approved"
    ORDER_DISPATCHED = "order_dispatched"
    ORDER_DELIVERED = "order_delivered"
    INVENTORY_LOW = "inventory_low"
    USER_ACTION = "user_action"
    SYSTEM_ALERT = "system_alert"
    PAYMENT_RECEIVED = "payment_received"
    RIDER_ASSIGNED = "rider_assigned"
    DELIVERY_DELAYED = "delivery_delayed"

class NotificationManager:
    """
    Comprehensive notification management system
    """
    
    def __init__(self):
        self.db_path = None
        
    def get_db(self):
        """Get database connection"""
        if 'db' not in g:
            db_path = current_app.config.get('DATABASE', 'instance/medivent.db')
            g.db = sqlite3.connect(db_path)
            g.db.row_factory = sqlite3.Row
            g.db.execute("PRAGMA foreign_keys = ON")
        return g.db
    
    def create_notification(self, user_id: int, notification_type: str, title: str, 
                          message: str, data: Dict = None, priority: NotificationPriority = NotificationPriority.MEDIUM) -> int:
        """
        Create a new notification
        
        Args:
            user_id: Target user ID
            notification_type: Type of notification
            title: Notification title
            message: Notification message
            data: Additional data (JSON)
            priority: Notification priority
            
        Returns:
            notification_id: ID of created notification
        """
        try:
            db = self.get_db()
            
            # Ensure notifications table exists
            self._ensure_notifications_table()
            
            cursor = db.execute('''
                INSERT INTO notifications (
                    user_id, notification_type, title, message, data, priority,
                    is_read, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id, notification_type, title, message, 
                json.dumps(data or {}), priority.value,
                0, datetime.now(), datetime.now()
            ))
            
            notification_id = cursor.lastrowid
            db.commit()
            
            return notification_id
            
        except Exception as e:
            print(f"Error creating notification: {e}")
            return None
    
    def get_user_notifications(self, user_id: int, limit: int = 20, 
                             unread_only: bool = False, notification_type: str = None) -> List[Dict]:
        """
        Get notifications for a user
        
        Args:
            user_id: User ID
            limit: Maximum number of notifications
            unread_only: Only return unread notifications
            notification_type: Filter by notification type
            
        Returns:
            List of notification dictionaries
        """
        try:
            db = self.get_db()
            
            # Build query
            query = '''
                SELECT id, user_id, notification_type, title, message, data, priority,
                       is_read, created_at, updated_at
                FROM notifications 
                WHERE user_id = ?
            '''
            params = [user_id]
            
            if unread_only:
                query += ' AND is_read = 0'
                
            if notification_type:
                query += ' AND notification_type = ?'
                params.append(notification_type)
                
            query += ' ORDER BY created_at DESC LIMIT ?'
            params.append(limit)
            
            notifications = db.execute(query, params).fetchall()
            
            # Convert to dictionaries
            result = []
            for notification in notifications:
                result.append({
                    'id': notification['id'],
                    'user_id': notification['user_id'],
                    'notification_type': notification['notification_type'],
                    'title': notification['title'],
                    'message': notification['message'],
                    'data': json.loads(notification['data'] or '{}'),
                    'priority': notification['priority'],
                    'is_read': bool(notification['is_read']),
                    'created_at': notification['created_at'],
                    'updated_at': notification['updated_at']
                })
                
            return result
            
        except Exception as e:
            print(f"Error getting user notifications: {e}")
            return []
    
    def mark_as_read(self, notification_id: int, user_id: int) -> bool:
        """
        Mark notification as read
        
        Args:
            notification_id: Notification ID
            user_id: User ID (for security)
            
        Returns:
            Success status
        """
        try:
            db = self.get_db()
            
            cursor = db.execute('''
                UPDATE notifications 
                SET is_read = 1, updated_at = ?
                WHERE id = ? AND user_id = ?
            ''', (datetime.now(), notification_id, user_id))
            
            db.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            print(f"Error marking notification as read: {e}")
            return False
    
    def mark_all_as_read(self, user_id: int) -> int:
        """
        Mark all notifications as read for a user
        
        Args:
            user_id: User ID
            
        Returns:
            Number of notifications marked as read
        """
        try:
            db = self.get_db()
            
            cursor = db.execute('''
                UPDATE notifications 
                SET is_read = 1, updated_at = ?
                WHERE user_id = ? AND is_read = 0
            ''', (datetime.now(), user_id))
            
            db.commit()
            return cursor.rowcount
            
        except Exception as e:
            print(f"Error marking all notifications as read: {e}")
            return 0
    
    def delete_notification(self, notification_id: int, user_id: int) -> bool:
        """
        Delete a notification
        
        Args:
            notification_id: Notification ID
            user_id: User ID (for security)
            
        Returns:
            Success status
        """
        try:
            db = self.get_db()
            
            cursor = db.execute('''
                DELETE FROM notifications 
                WHERE id = ? AND user_id = ?
            ''', (notification_id, user_id))
            
            db.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            print(f"Error deleting notification: {e}")
            return False
    
    def archive_notification(self, notification_id: int, user_id: int) -> bool:
        """
        Archive a notification (mark as archived)
        
        Args:
            notification_id: Notification ID
            user_id: User ID (for security)
            
        Returns:
            Success status
        """
        try:
            db = self.get_db()
            
            # Add archived column if it doesn't exist
            try:
                db.execute('ALTER TABLE notifications ADD COLUMN is_archived INTEGER DEFAULT 0')
                db.commit()
            except sqlite3.OperationalError:
                pass  # Column already exists
            
            cursor = db.execute('''
                UPDATE notifications 
                SET is_archived = 1, updated_at = ?
                WHERE id = ? AND user_id = ?
            ''', (datetime.now(), notification_id, user_id))
            
            db.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            print(f"Error archiving notification: {e}")
            return False
    
    def get_notification_stats(self, user_id: int) -> Dict:
        """
        Get notification statistics for a user
        
        Args:
            user_id: User ID
            
        Returns:
            Statistics dictionary
        """
        try:
            db = self.get_db()
            
            # Get total count
            total = db.execute('''
                SELECT COUNT(*) as count FROM notifications WHERE user_id = ?
            ''', (user_id,)).fetchone()['count']
            
            # Get unread count
            unread = db.execute('''
                SELECT COUNT(*) as count FROM notifications 
                WHERE user_id = ? AND is_read = 0
            ''', (user_id,)).fetchone()['count']
            
            # Get counts by type
            type_counts = db.execute('''
                SELECT notification_type, COUNT(*) as count 
                FROM notifications 
                WHERE user_id = ? 
                GROUP BY notification_type
            ''', (user_id,)).fetchall()
            
            type_stats = {}
            for row in type_counts:
                type_stats[row['notification_type']] = row['count']
            
            return {
                'total': total,
                'unread': unread,
                'read': total - unread,
                'by_type': type_stats
            }
            
        except Exception as e:
            print(f"Error getting notification stats: {e}")
            return {'total': 0, 'unread': 0, 'read': 0, 'by_type': {}}
    
    def _ensure_notifications_table(self):
        """Ensure notifications table exists with proper schema"""
        try:
            db = self.get_db()
            
            db.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    notification_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    data TEXT,
                    priority TEXT DEFAULT 'medium',
                    is_read INTEGER DEFAULT 0,
                    is_archived INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            db.commit()
            
        except Exception as e:
            print(f"Error ensuring notifications table: {e}")
