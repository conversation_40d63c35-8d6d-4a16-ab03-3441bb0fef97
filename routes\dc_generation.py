#!/usr/bin/env python3
"""
DC Generation Routes - Clean Implementation
Handles all DC generation web routes with proper error handling
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, g
from flask_login import login_required, current_user
import sqlite3
from models.dc_models import DCGenerator, BatchSelector, InventoryManager, DCGenerationError
import json
from datetime import datetime

# Database helper function with proper connection management
def get_db():
    if 'db' not in g:
        g.db = sqlite3.connect('instance/medivent.db', timeout=30.0)
        g.db.row_factory = sqlite3.Row
        g.db.execute("PRAGMA foreign_keys = ON")
        g.db.execute("PRAGMA journal_mode = WAL")  # Better concurrency
    return g.db

dc_generation_bp = Blueprint('dc_generation', __name__)


@dc_generation_bp.route('/orders/<order_id>/batch-selection')
@login_required
def batch_selection(order_id):
    """Main batch selection interface - Clean and Simple"""
    try:
        db = get_db()
        
        # Get order details
        order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
        if not order:
            flash('Order not found', 'error')
            return redirect(url_for('dc_pending'))
        
        # Check if order is eligible for DC generation
        if order['status'] != 'Approved':
            flash('Order must be approved before DC generation', 'warning')
            return redirect(url_for('dc_pending'))
        
        # Check if DC already exists
        existing_dc = db.execute(
            'SELECT dc_number FROM delivery_challans WHERE order_id = ?', 
            (order_id,)
        ).fetchone()
        
        if existing_dc:
            flash(f'DC already generated: {existing_dc["dc_number"]}', 'info')
            return redirect(url_for('dc_generation.view_dc', dc_number=existing_dc['dc_number']))
        
        # Get inventory data
        try:
            inventory_data = BatchSelector.get_order_inventory_data(order_id)
        except DCGenerationError as e:
            flash(f'Error loading inventory: {str(e)}', 'error')
            return redirect(url_for('dc_pending'))
        
        # Check if any inventory is available
        has_inventory = any(
            product_data['warehouses'] 
            for product_data in inventory_data.values()
        )
        
        if not has_inventory:
            flash('No inventory available for this order. Please check stock levels.', 'warning')
            return redirect(url_for('dc_pending'))
        
        return render_template('dc/batch_selection.html', 
                             order=dict(order),
                             inventory_data=inventory_data,
                             now=datetime.now())
        
    except Exception as e:
        flash(f'Error loading batch selection: {str(e)}', 'error')
        return redirect(url_for('dc_pending'))


@dc_generation_bp.route('/orders/<order_id>/allocate-batches', methods=['POST'])
@login_required
def allocate_batches(order_id):
    """Save batch allocations"""
    try:
        # Parse batch selections from form
        batch_selections = {}
        
        for key, value in request.form.items():
            if key.startswith('batch_') and value:
                try:
                    # Parse key: batch_{product_id}_{inventory_id}
                    parts = key.split('_')
                    if len(parts) >= 3:
                        product_id = parts[1]
                        inventory_id = '_'.join(parts[2:])  # Handle inventory IDs with underscores
                        quantity = int(value)
                        
                        if quantity > 0:
                            if product_id not in batch_selections:
                                batch_selections[product_id] = []
                            
                            batch_selections[product_id].append({
                                'inventory_id': inventory_id,
                                'quantity': quantity
                            })
                except (ValueError, IndexError):
                    continue
        
        if not batch_selections:
            flash('No batch selections made. Please select at least one batch.', 'warning')
            return redirect(url_for('dc_generation.batch_selection', order_id=order_id))
        
        # Validate selections
        try:
            is_valid, errors = BatchSelector.validate_batch_selections(order_id, batch_selections)
            if not is_valid:
                for error in errors:
                    flash(error, 'error')
                return redirect(url_for('dc_generation.batch_selection', order_id=order_id))
        except DCGenerationError as e:
            flash(f'Validation error: {str(e)}', 'error')
            return redirect(url_for('dc_generation.batch_selection', order_id=order_id))
        
        # Store selections in session or temporary table for review
        # For now, proceed directly to DC generation
        return redirect(url_for('dc_generation.generate_dc', order_id=order_id, 
                               selections=json.dumps(batch_selections)))
        
    except Exception as e:
        flash(f'Error processing batch allocations: {str(e)}', 'error')
        return redirect(url_for('dc_generation.batch_selection', order_id=order_id))


@dc_generation_bp.route('/orders/<order_id>/generate-dc')
@login_required
def generate_dc(order_id):
    """Generate final delivery challan"""
    try:
        # Get batch selections from query parameter
        selections_json = request.args.get('selections')
        if not selections_json:
            flash('No batch selections found. Please select batches first.', 'warning')
            return redirect(url_for('dc_generation.batch_selection', order_id=order_id))
        
        try:
            batch_selections = json.loads(selections_json)
        except json.JSONDecodeError:
            flash('Invalid batch selection data.', 'error')
            return redirect(url_for('dc_generation.batch_selection', order_id=order_id))
        
        # Generate DC
        try:
            dc_number = DCGenerator.create_delivery_challan(
                order_id, batch_selections, current_user.username
            )
            
            flash(f'Delivery Challan {dc_number} generated successfully!', 'success')
            return redirect(url_for('dc_generation.view_dc', dc_number=dc_number))
            
        except DCGenerationError as e:
            flash(f'Error generating DC: {str(e)}', 'error')
            return redirect(url_for('dc_generation.batch_selection', order_id=order_id))
        
    except Exception as e:
        flash(f'Unexpected error: {str(e)}', 'error')
        return redirect(url_for('dc_generation.batch_selection', order_id=order_id))


@dc_generation_bp.route('/delivery-challans')
@login_required
def list_dcs():
    """List all delivery challans"""
    try:
        db = get_db()
        
        challans = db.execute('''
            SELECT dc.*, o.customer_name, o.order_date, o.order_amount
            FROM delivery_challans dc
            JOIN orders o ON dc.order_id = o.order_id
            ORDER BY dc.created_date DESC
        ''').fetchall()
        
        return render_template('dc/dc_list.html', 
                             challans=[dict(row) for row in challans],
                             now=datetime.now())
        
    except Exception as e:
        flash(f'Error loading delivery challans: {str(e)}', 'error')
        return redirect(url_for('dashboard'))


@dc_generation_bp.route('/delivery-challans/<dc_number>/view')
@login_required
def view_dc(dc_number):
    """View delivery challan details"""
    try:
        db = get_db()
        
        # Get DC details
        dc = db.execute('''
            SELECT dc.*, o.customer_name, o.customer_address, o.customer_phone,
                   o.order_date, o.order_amount
            FROM delivery_challans dc
            JOIN orders o ON dc.order_id = o.order_id
            WHERE dc.dc_number = ?
        ''', (dc_number,)).fetchone()
        
        if not dc:
            flash('Delivery challan not found', 'error')
            return redirect(url_for('dc_generation.list_dcs'))
        
        # Parse batch details
        batch_details = []
        if dc['batch_details']:
            try:
                allocations = json.loads(dc['batch_details'])
                
                for allocation in allocations:
                    # Get detailed batch info
                    batch_info = db.execute('''
                        SELECT i.*, p.name as product_name, p.strength, w.name as warehouse_name
                        FROM inventory i
                        JOIN products p ON i.product_id = p.product_id
                        JOIN warehouses w ON i.warehouse_id = w.warehouse_id
                        WHERE i.inventory_id = ?
                    ''', (allocation['inventory_id'],)).fetchone()
                    
                    if batch_info:
                        batch_details.append({
                            **dict(batch_info),
                            'allocated_quantity': allocation['quantity']
                        })
                        
            except (json.JSONDecodeError, KeyError):
                pass
        
        return render_template('dc/dc_view.html',
                             dc=dict(dc),
                             batch_details=batch_details,
                             now=datetime.now())
        
    except Exception as e:
        flash(f'Error viewing delivery challan: {str(e)}', 'error')
        return redirect(url_for('dc_generation.list_dcs'))


@dc_generation_bp.route('/delivery-challans/<dc_number>/pdf')
@login_required
def download_dc_pdf(dc_number):
    """Download DC as PDF"""
    try:
        from utils.pdf_generator import generate_dc_pdf
        import os
        from flask import send_file

        # Generate PDF
        pdf_path = generate_dc_pdf(dc_number)

        if pdf_path and os.path.exists(pdf_path):
            return send_file(
                pdf_path,
                mimetype='application/pdf',
                as_attachment=True,
                download_name=f'{dc_number}.pdf'
            )
        else:
            flash('PDF file not found or could not be generated', 'error')
            return redirect(url_for('dc_generation.view_dc', dc_number=dc_number))

    except Exception as e:
        flash(f'Error generating PDF: {str(e)}', 'error')
        return redirect(url_for('dc_generation.view_dc', dc_number=dc_number))


# API Routes for AJAX functionality

@dc_generation_bp.route('/api/inventory/<product_id>')
@login_required
def api_get_inventory(product_id):
    """API endpoint to get inventory for a product"""
    try:
        warehouse_id = request.args.get('warehouse_id')
        inventory = InventoryManager.get_available_inventory(product_id, warehouse_id)
        
        return jsonify({
            'success': True,
            'inventory': inventory
        })
        
    except DCGenerationError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Unexpected error: {str(e)}'
        }), 500


@dc_generation_bp.route('/api/validate-selection', methods=['POST'])
@login_required
def api_validate_selection():
    """API endpoint to validate batch selections"""
    try:
        data = request.get_json()
        order_id = data.get('order_id')
        selections = data.get('selections', {})
        
        if not order_id:
            return jsonify({
                'success': False,
                'error': 'Order ID required'
            }), 400
        
        is_valid, errors = BatchSelector.validate_batch_selections(order_id, selections)
        
        return jsonify({
            'success': True,
            'valid': is_valid,
            'errors': errors
        })
        
    except DCGenerationError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Unexpected error: {str(e)}'
        }), 500
