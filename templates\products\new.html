{% extends 'base.html' %}

{% block title %}Add New Product - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
<style>
    .main-content {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin: 20px;
        padding: 30px;
    }
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.4);
    }
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    .card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border-radius: 15px 15px 0 0 !important;
        padding: 20px;
    }
    .form-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    .required {
        color: #dc3545;
    }
    .upload-area {
        border: 2px dashed #007bff;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        background-color: #f8f9ff;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .upload-area:hover {
        background-color: #e3f2fd;
        border-color: #0056b3;
    }
    .price-input {
        position: relative;
    }
    .price-input .currency {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-weight: 600;
    }
    .price-input input {
        padding-left: 35px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1"><i class="fas fa-plus-circle text-primary"></i> Add New Product</h2>
            <p class="text-muted">Create a new product entry in the system</p>
        </div>
        <div>
            <a href="{{ url_for('products') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
            <button class="btn btn-outline-primary" onclick="saveDraft()">
                <i class="fas fa-save"></i> Save as Draft
            </button>
        </div>
    </div>

    <!-- Enhanced Add Product Form -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-info-circle"></i> Product Information</h5>
        </div>
        <div class="card-body">
            <form method="post" action="{{ url_for('new_product') }}" enctype="multipart/form-data">
                <div class="row">
                    <!-- Professional Product Image Upload -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Product Image <span class="text-muted">(Optional)</span></label>
                            <div class="upload-area" id="uploadArea" onclick="document.getElementById('product_image').click()">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <p class="mb-2"><strong>Click to upload</strong></p>
                                <p class="text-muted small">PNG, JPG, JPEG, GIF, WebP up to 16MB</p>
                                <input type="file" id="product_image" name="product_image" class="d-none"
                                       accept="image/png,image/jpeg,image/jpg,image/gif,image/webp">
                            </div>
                            <div id="imagePreview" class="mt-3" style="display: none;">
                                <div class="card">
                                    <div class="card-body p-2">
                                        <img id="previewImg" src="" class="img-fluid rounded" style="max-height: 150px;">
                                        <div class="mt-2">
                                            <small class="text-muted" id="imageInfo"></small>
                                            <br>
                                            <button type="button" class="btn btn-sm btn-outline-danger mt-1" onclick="removeImage()">
                                                <i class="fas fa-trash"></i> Remove
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Details -->
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Product Name <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" placeholder="Enter product name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="strength">Strength <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="strength" name="strength" placeholder="e.g., 500mg, 10ml, 100IU" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="generic_name">Generic Name <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="generic_name" name="generic_name"
                                           placeholder="Enter generic name (e.g., Paracetamol, Amoxicillin)" required>
                                    <small class="form-text text-muted">Enter the active pharmaceutical ingredient or generic name</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="manufacturer">Manufacturer <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="manufacturer" name="manufacturer" placeholder="Enter manufacturer" value="Medivent" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="division_id">Division <span class="required">*</span></label>
                                    <select class="form-control" id="division_id" name="division_id" required>
                                        <option value="">Select Division</option>
                                        {% for division in divisions %}
                                        <option value="{{ division.division_id }}">{{ division.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing Information -->
                <hr class="my-4">
                <h6 class="text-primary mb-3"><i class="fas fa-rupee-sign"></i> Pricing Information</h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="mrp">MRP (Maximum Retail Price) <span class="required">*</span></label>
                            <div class="price-input">
                                <span class="currency">Rs.</span>
                                <input type="number" class="form-control" id="mrp" name="mrp" placeholder="0.00" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="tp_rate">TP Rate (Trade Price) <span class="required">*</span></label>
                            <div class="price-input">
                                <span class="currency">Rs.</span>
                                <input type="number" class="form-control" id="tp_rate" name="tp_rate" placeholder="0.00" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="unit_price">ASP (Average Selling Price) <span class="required">*</span></label>
                            <div class="price-input">
                                <span class="currency">Rs.</span>
                                <input type="number" class="form-control" id="unit_price" name="unit_price" placeholder="0.00" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="pack_size">Pack Size <span class="required">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="pack_size" name="pack_size" placeholder="e.g., 10 tablets, 100ml" required>
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fas fa-box"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Additional Information -->
                <hr class="my-4">
                <h6 class="text-primary mb-3"><i class="fas fa-info"></i> Additional Information</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="description">Product Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4" placeholder="Enter product description, usage instructions, etc."></textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="unit_of_measure">Unit of Measure</label>
                                    <select class="form-control" id="unit_of_measure" name="unit_of_measure">
                                        <option value="Tablet">Tablet</option>
                                        <option value="Capsule">Capsule</option>
                                        <option value="Bottle">Bottle</option>
                                        <option value="Vial">Vial</option>
                                        <option value="Ampule">Ampule</option>
                                        <option value="Box">Box</option>
                                        <option value="Pack">Pack</option>
                                    </select>
                                </div>
                            </div>

                        </div>
                        <div class="form-group">
                            <label for="product_code">Product Code/SKU</label>
                            <input type="text" class="form-control" id="product_code" name="product_code" placeholder="Auto-generated or enter manually">
                        </div>

                    </div>
                </div>

                <!-- Form Actions -->
                <hr class="my-4">
                <div class="d-flex justify-content-end">
                    <a href="{{ url_for('products') }}" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="button" class="btn btn-outline-primary me-3" onclick="saveDraft()">
                        <i class="fas fa-save"></i> Save as Draft
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Professional Image Upload System
let uploadedImageData = null;

document.getElementById('product_image').addEventListener('change', function(e) {
    if (e.target.files.length > 0) {
        const file = e.target.files[0];

        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('Invalid file type. Please upload PNG, JPG, JPEG, GIF, or WebP images.');
            return;
        }

        // Validate file size (16MB)
        if (file.size > 16 * 1024 * 1024) {
            alert('File size too large. Please upload images smaller than 16MB.');
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImg').src = e.target.result;
            document.getElementById('imageInfo').textContent = `${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
            document.getElementById('imagePreview').style.display = 'block';
            document.getElementById('uploadArea').style.display = 'none';

            // Store file data for upload
            uploadedImageData = file;
        };
        reader.readAsDataURL(file);
    }
});

function removeImage() {
    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('uploadArea').style.display = 'block';
    document.getElementById('product_image').value = '';
    uploadedImageData = null;
}

// Enhanced form submission with image upload
document.querySelector('form').addEventListener('submit', function(e) {
    // Show loading state for any form submission
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    submitBtn.disabled = true;

    // Let the form submit normally with enctype="multipart/form-data"
    // The server will handle the image upload
});

// Auto-calculate TP Rate from MRP (MRP * 0.85 = TP Rate)
document.getElementById('mrp').addEventListener('input', function() {
    const mrp = parseFloat(this.value) || 0;
    if (mrp > 0) {
        // Calculate TP Rate: MRP * 0.85 (MRP - MRP * 0.15) (unchangeable until product batch ends)
        const tpRate = mrp * 0.85; // 85% of MRP
        const tpInput = document.getElementById('tp_rate');
        tpInput.value = tpRate.toFixed(2);
        tpInput.readOnly = true;
        tpInput.style.backgroundColor = '#f8f9fa';
        tpInput.title = 'Auto-calculated: MRP × 0.85 (unchangeable until batch ends)';

        // Calculate ASP after TP Rate is set
        calculateASP();
    }
});

// Manual TP Rate input (disabled by default)
document.getElementById('tp_rate').addEventListener('input', calculateASP);

function calculateASP() {
    const mrp = parseFloat(document.getElementById('mrp').value) || 0;
    const tpRate = parseFloat(document.getElementById('tp_rate').value) || 0;

    if (mrp > 0 && tpRate > 0) {
        // Calculate ASP as average of MRP and TP Rate
        const asp = (mrp + tpRate) / 2;
        document.getElementById('asp').value = asp.toFixed(2);
    }
}

// Auto-calculate margins and validate pricing
document.querySelectorAll('.price-input input').forEach(input => {
    input.addEventListener('input', function() {
        console.log('Price updated:', this.value);
        // Add validation logic here if needed
    });
});

// Save as draft functionality
function saveDraft() {
    alert('🚀 Draft saving functionality would be implemented here!');
    // This would save the form data to localStorage or send to server
}

// Form validation enhancement
document.querySelector('form').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields marked with *');
    }
});
</script>
{% endblock %}
