{% extends "base.html" %}

{% block title %}Products Gallery{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0">
                                <i class="fas fa-th-large"></i> Products Gallery
                            </h4>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ url_for('new_product') }}" class="btn btn-light">
                                <i class="fas fa-plus"></i> Add Product
                            </a>
                            <a href="{{ url_for('inventory.index') }}" class="btn btn-info">
                                <i class="fas fa-warehouse"></i> Inventory
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Search and Filter Section -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="GET" action="{{ url_for('products') }}" class="form-inline">
                                <div class="input-group" style="width: 100%;">
                                    <input type="text" name="q" class="form-control"
                                           placeholder="Search products by name, generic name, category..."
                                           value="{{ search_query }}" id="search-input">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-outline-success">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        {% if search_query %}
                                        <a href="{{ url_for('products') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-info dropdown-toggle" data-toggle="dropdown">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{{ url_for('products') }}">All Products</a>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?category=Tablets">Tablets</a>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?category=Capsules">Capsules</a>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?category=Syrup">Syrups</a>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?category=Injections">Injections</a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?status=active">Active Only</a>
                                    <a class="dropdown-item" href="{{ url_for('products') }}?status=inactive">Inactive Only</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5>{{ products|length }}</h5>
                                    <small>Total Products</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h5>{{ products|selectattr('is_active', 'equalto', True)|list|length if products else 0 }}</h5>
                                    <small>Active Products</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h5>{{ products|selectattr('type', 'equalto', 'Tablets')|list|length if products else 0 }}</h5>
                                    <small>Tablets</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5>{{ products|selectattr('type', 'equalto', 'Capsules')|list|length if products else 0 }}</h5>
                                    <small>Capsules</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products List -->
                    {% if products %}
                        <div class="row">
                            {% for product in products %}
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex align-items-start">
                                            <div class="product-image-container mr-3">
                                                {% if product.image_url %}
                                                    <img src="{{ product.image_url }}"
                                                         alt="{{ product.name }}"
                                                         class="product-image"
                                                         onclick="showImageModal('{{ product.image_url }}', '{{ product.name }}')"
                                                         title="Click to view full size">
                                                {% else %}
                                                    <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                                        <i class="fas fa-pills text-muted"></i>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="card-title mb-2">{{ product.name }}</h6>
                                                <p class="text-muted small mb-1">
                                                    <strong>Category:</strong> {{ product.category or 'N/A' }}
                                                </p>
                                                <p class="text-muted small mb-1">
                                                    <strong>Manufacturer:</strong> {{ product.manufacturer or 'N/A' }}
                                                </p>
                                                <p class="text-muted small mb-2">
                                                    <strong>Price:</strong> ${{ "%.2f"|format(product.unit_price or 0) }}
                                                </p>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="{{ url_for('update_product', product_id=product.product_id) }}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteProduct('{{ product.product_id }}', '{{ product.name }}')">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Products Found</h5>
                            <p class="text-muted">Start by adding your first product to the inventory.</p>
                            <a href="{{ url_for('new_product') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add First Product
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="imageModalLabel">
                    <i class="fas fa-image"></i> Product Image
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center p-4">
                <img id="modalImage" src="" class="img-fluid rounded shadow" alt="Product Image" style="max-height: 70vh; object-fit: contain;">
                <div class="mt-4">
                    <a id="downloadLink" href="" class="btn btn-success btn-lg mr-3" download>
                        <i class="fas fa-download"></i> Download Original
                    </a>
                    <button type="button" class="btn btn-secondary btn-lg" data-dismiss="modal">
                        <i class="fas fa-times"></i> Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.product-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
    padding: 5px;
}
.product-image:hover {
    transform: scale(1.1);
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}
.product-image-container {
    flex-shrink: 0;
}
</style>

<script>
// Image modal functionality
function showImageModal(imageUrl, productName) {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalLabel = document.getElementById('imageModalLabel');
    const downloadLink = document.getElementById('downloadLink');

    modalImage.src = imageUrl;
    modalLabel.innerHTML = `<i class="fas fa-image"></i> ${productName} - Product Image`;
    downloadLink.href = imageUrl;
    downloadLink.download = `${productName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_image`;

    $('#imageModal').modal('show');
}

// Delete product functionality with dependency checking
function deleteProduct(productId, productName) {
    if (confirm(`Are you sure you want to delete "${productName}"?\\n\\nThis action cannot be undone.`)) {
        const button = event.target;
        const originalContent = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
        button.disabled = true;

        fetch(`/products/${productId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message and reload
                alert('Product deleted successfully!');
                location.reload();
            } else {
                // Check if deletion failed due to dependencies
                if (data.alternative === 'deactivate' && data.dependencies) {
                    const dependencyList = data.dependencies.join(', ');
                    const deactivateConfirm = confirm(
                        `Cannot delete "${productName}" because it is referenced by: ${dependencyList}.\\n\\n` +
                        `Would you like to deactivate the product instead? This will hide it from new orders while preserving historical data.`
                    );

                    if (deactivateConfirm) {
                        // Call deactivate endpoint
                        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deactivating...';

                        fetch(`/products/${productId}/deactivate`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => response.json())
                        .then(deactivateData => {
                            if (deactivateData.success) {
                                alert('Product deactivated successfully!');
                                location.reload();
                            } else {
                                alert('Error deactivating product: ' + (deactivateData.message || 'Unknown error'));
                                button.innerHTML = originalContent;
                                button.disabled = false;
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Network error during deactivation. Please try again.');
                            button.innerHTML = originalContent;
                            button.disabled = false;
                        });
                    } else {
                        button.innerHTML = originalContent;
                        button.disabled = false;
                    }
                } else {
                    alert('Error deleting product: ' + (data.message || 'Unknown error'));
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Network error. Please check your connection and try again.');
            button.innerHTML = originalContent;
            button.disabled = false;
        });
    }
}
</script>
{% endblock %}
