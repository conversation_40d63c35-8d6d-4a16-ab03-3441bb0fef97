{% extends "base.html" %}

{% block title %}Product Details - {{ product.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Product Header -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-0">
                                <i class="fas fa-pills"></i> {{ product.name }}
                            </h4>
                            <small>{{ product.product_id }} | {{ product.generic_name or 'No Generic Name' }}</small>
                        </div>
                        <div class="col-md-4 text-right">
                            <span class="badge badge-{% if product.is_active != False %}success{% else %}danger{% endif %} badge-lg">
                                {% if product.is_active != False %}Active{% else %}Inactive{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h6><strong>Basic Information</strong></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Product ID:</strong></td>
                                    <td>{{ product.product_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Product Name:</strong></td>
                                    <td>{{ product.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Generic Name:</strong></td>
                                    <td>{{ product.generic_name or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Strength:</strong></td>
                                    <td>{{ product.strength or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Unit of Measure:</strong></td>
                                    <td>{{ product.unit_of_measure or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td>{{ product.category or 'General' }}</td>
                                </tr>
                                {% if product.division_name %}
                                <tr>
                                    <td><strong>Division:</strong></td>
                                    <td>{{ product.division_name }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>Manufacturer:</strong></td>
                                    <td>{{ product.manufacturer or 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                        
                        <!-- Pricing & Stock Information -->
                        <div class="col-md-6">
                            <h6><strong>Pricing & Stock Information</strong></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Cost Price:</strong></td>
                                    <td>₹{{ "%.2f"|format(product.cost_price or 0) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Selling Price:</strong></td>
                                    <td>₹{{ "%.2f"|format(product.selling_price or 0) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Current Stock:</strong></td>
                                    <td>
                                        <span class="badge badge-{% if inventory_summary.available_stock > 0 %}success{% else %}danger{% endif %}">
                                            {{ inventory_summary.available_stock or 0 }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Reorder Level:</strong></td>
                                    <td>{{ product.reorder_level or 10 }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Max Stock Level:</strong></td>
                                    <td>{{ product.max_stock_level or 100 }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Additional Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Manufacturer:</strong></td>
                                    <td>{{ product.manufacturer or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Country:</strong></td>
                                    <td>{{ product.country or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ product.created_at.strftime('%Y-%m-%d %H:%M') if product.created_at else 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Last Updated:</strong></td>
                                    <td>{{ product.updated_at.strftime('%Y-%m-%d %H:%M') if product.updated_at else 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Division Manager:</strong></td>
                                    <td>{{ product.division_manager or 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Division Category:</strong></td>
                                    <td>{{ product.division_category or 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('products.product_management') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Products
                        </a>
                        <a href="{{ url_for('products.edit_product', product_id=product.product_id) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Product
                        </a>
                        <a href="{{ url_for('inventory.new_inventory', product_id=product.product_id) }}" class="btn btn-info">
                            <i class="fas fa-plus"></i> Add Stock
                        </a>
                        <a href="{{ url_for('inventory.product_inventory', product_id=product.product_id) }}" class="btn btn-warning">
                            <i class="fas fa-warehouse"></i> View All Inventory
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
