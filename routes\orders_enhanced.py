#!/usr/bin/env python3
"""
Enhanced Order Management Routes
Professional order management sub-routes with complete frontend/backend integration
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from werkzeug.exceptions import abort
from datetime import datetime, timedelta
import sqlite3
import json
from functools import wraps

# Import database utilities
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.db import get_db

orders_enhanced_bp = Blueprint('orders_enhanced', __name__, url_prefix='/orders')

# Order status constants
ORDER_STATUSES = [
    'Placed', 'Confirmed', 'Processing', 'Approved', 'Dispatched', 
    'Out for Delivery', 'Delivered', 'Cancelled', 'Returned'
]

def login_required(f):
    """Decorator to require login for routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@orders_enhanced_bp.route('/update/<order_id>', methods=['GET', 'POST'])
@login_required
def update_order(order_id):
    """Enhanced Order Update functionality with complete validation"""
    db = get_db()
    
    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found.', 'danger')
        return redirect(url_for('orders.index'))
    
    if request.method == 'POST':
        try:
            # Get form data with validation
            customer_name = request.form.get('customer_name', '').strip()
            customer_address = request.form.get('customer_address', '').strip()
            customer_phone = request.form.get('customer_phone', '').strip()
            customer_email = request.form.get('customer_email', '').strip()
            status = request.form.get('status', '').strip()
            payment_method = request.form.get('payment_method', '').strip()
            payment_status = request.form.get('payment_status', '').strip()
            sales_agent = request.form.get('sales_agent', '').strip()
            notes = request.form.get('notes', '').strip()
            
            # Validation
            if not customer_name:
                flash('Customer name is required.', 'danger')
                return redirect(url_for('orders_enhanced.update_order', order_id=order_id))
            
            if status not in ORDER_STATUSES:
                flash('Invalid order status.', 'danger')
                return redirect(url_for('orders_enhanced.update_order', order_id=order_id))
            
            # Update order
            db.execute('''
                UPDATE orders SET
                    customer_name = ?,
                    customer_address = ?,
                    customer_phone = ?,
                    customer_email = ?,
                    status = ?,
                    payment_method = ?,
                    payment_status = ?,
                    sales_agent = ?,
                    notes = ?,
                    last_updated = CURRENT_TIMESTAMP,
                    updated_by = ?
                WHERE order_id = ?
            ''', (customer_name, customer_address, customer_phone, customer_email,
                  status, payment_method, payment_status, sales_agent, notes,
                  session.get('username', 'system'), order_id))
            
            # Log activity
            db.execute('''
                INSERT INTO activity_logs (username, action, entity_id, details, module)
                VALUES (?, ?, ?, ?, ?)
            ''', (session.get('username', 'system'), 'UPDATE_ORDER', order_id,
                  f'Order updated: status changed to {status}', 'orders'))
            
            db.commit()
            flash('Order updated successfully!', 'success')
            return redirect(url_for('orders.view_order', order_id=order_id))
            
        except Exception as e:
            db.rollback()
            flash(f'Error updating order: {str(e)}', 'danger')
            return redirect(url_for('orders_enhanced.update_order', order_id=order_id))
    
    # GET request - show update form
    try:
        # Use real-time product service for consistent data
        try:
            from utils.product_realtime_service import get_products_with_inventory_realtime
            products = get_products_with_inventory_realtime(db)
        except Exception as e:
            # Fallback to basic query if real-time service fails
            print(f"Real-time service failed, using fallback: {e}")
            products = db.execute('SELECT * FROM products ORDER BY name').fetchall()

        order_items = db.execute('''
            SELECT oi.*, p.name as product_name
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            WHERE oi.order_id = ?
        ''', (order_id,)).fetchall()
    except Exception as e:
        products = []
        order_items = []
        flash(f'Error loading order data: {str(e)}', 'warning')
    
    return render_template('orders/enhanced_update.html',
                          order=order,
                          order_items=order_items,
                          products=products,
                          statuses=ORDER_STATUSES)

@orders_enhanced_bp.route('/search', methods=['GET', 'POST'])
@login_required
def advanced_search():
    """Advanced search with filters and pagination"""
    db = get_db()
    
    # Default values
    orders = []
    total_count = 0
    page = int(request.args.get('page', 1))
    per_page = 20
    
    # Search parameters
    search_query = request.args.get('q', '').strip()
    status_filter = request.args.get('status', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    customer_filter = request.args.get('customer', '').strip()
    amount_min = request.args.get('amount_min', '')
    amount_max = request.args.get('amount_max', '')
    
    try:
        # Build search query
        where_conditions = []
        params = []
        
        if search_query:
            where_conditions.append('''
                (order_id LIKE ? OR customer_name LIKE ? OR customer_phone LIKE ? 
                 OR invoice_number LIKE ? OR sales_agent LIKE ?)
            ''')
            search_param = f'%{search_query}%'
            params.extend([search_param] * 5)
        
        if status_filter and status_filter in ORDER_STATUSES:
            where_conditions.append('status = ?')
            params.append(status_filter)
        
        if date_from:
            where_conditions.append('DATE(order_date) >= ?')
            params.append(date_from)
        
        if date_to:
            where_conditions.append('DATE(order_date) <= ?')
            params.append(date_to)
        
        if customer_filter:
            where_conditions.append('customer_name LIKE ?')
            params.append(f'%{customer_filter}%')
        
        if amount_min:
            where_conditions.append('order_amount >= ?')
            params.append(float(amount_min))
        
        if amount_max:
            where_conditions.append('order_amount <= ?')
            params.append(float(amount_max))
        
        # Build final query
        base_query = 'SELECT * FROM orders'
        count_query = 'SELECT COUNT(*) FROM orders'
        
        if where_conditions:
            where_clause = ' WHERE ' + ' AND '.join(where_conditions)
            base_query += where_clause
            count_query += where_clause
        
        # Get total count
        total_count = db.execute(count_query, params).fetchone()[0]
        
        # Get paginated results
        offset = (page - 1) * per_page
        final_query = base_query + ' ORDER BY order_date DESC LIMIT ? OFFSET ?'
        orders = db.execute(final_query, params + [per_page, offset]).fetchall()
        
    except Exception as e:
        flash(f'Search error: {str(e)}', 'danger')
        orders = []
        total_count = 0
    
    # Calculate pagination
    total_pages = (total_count + per_page - 1) // per_page
    has_prev = page > 1
    has_next = page < total_pages
    
    return render_template('orders/enhanced_search.html',
                          orders=orders,
                          search_query=search_query,
                          status_filter=status_filter,
                          date_from=date_from,
                          date_to=date_to,
                          customer_filter=customer_filter,
                          amount_min=amount_min,
                          amount_max=amount_max,
                          statuses=ORDER_STATUSES,
                          page=page,
                          total_pages=total_pages,
                          total_count=total_count,
                          has_prev=has_prev,
                          has_next=has_next)

@orders_enhanced_bp.route('/history/<order_id>')
@login_required
def order_history(order_id):
    """Complete order history with pagination and detailed activity logs"""
    db = get_db()
    
    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found.', 'danger')
        return redirect(url_for('orders.index'))
    
    try:
        # Get comprehensive activity logs
        logs = db.execute('''
            SELECT * FROM activity_logs 
            WHERE entity_id = ? 
            ORDER BY timestamp DESC
        ''', (order_id,)).fetchall()
        
        # Get order items history
        order_items = db.execute('''
            SELECT oi.*, p.name as product_name 
            FROM order_items oi 
            LEFT JOIN products p ON oi.product_id = p.product_id 
            WHERE oi.order_id = ?
        ''', (order_id,)).fetchall()
        
        # Get related documents
        invoice = db.execute('SELECT * FROM invoices WHERE order_id = ?', (order_id,)).fetchone()
        challan = db.execute('SELECT * FROM challans WHERE order_id = ?', (order_id,)).fetchone()
        
    except Exception as e:
        flash(f'Error loading order history: {str(e)}', 'warning')
        logs = []
        order_items = []
        invoice = None
        challan = None
    
    return render_template('orders/enhanced_history.html',
                          order=order,
                          logs=logs,
                          order_items=order_items,
                          invoice=invoice,
                          challan=challan,
                          statuses=ORDER_STATUSES)

@orders_enhanced_bp.route('/invoice-challan/<order_id>')
@login_required
def invoice_challan_management(order_id):
    """Professional Invoice and Challan Management"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found.', 'danger')
        return redirect(url_for('orders.index'))

    try:
        # Get order items for invoice/challan
        order_items = db.execute('''
            SELECT oi.*, p.name as product_name, p.strength, p.manufacturer
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            WHERE oi.order_id = ?
        ''', (order_id,)).fetchall()

        # Get existing invoice
        invoice = db.execute('SELECT * FROM invoices WHERE order_id = ?', (order_id,)).fetchone()

        # Get existing challan
        challan = db.execute('SELECT * FROM challans WHERE order_id = ?', (order_id,)).fetchone()

        # Calculate totals
        subtotal = sum(float(item['line_total'] or 0) for item in order_items)
        tax_rate = 18.0  # GST rate
        tax_amount = subtotal * (tax_rate / 100)
        total_amount = subtotal + tax_amount

    except Exception as e:
        flash(f'Error loading invoice/challan data: {str(e)}', 'warning')
        order_items = []
        invoice = None
        challan = None
        subtotal = tax_amount = total_amount = 0

    return render_template('orders/invoice_challan.html',
                          order=order,
                          order_items=order_items,
                          invoice=invoice,
                          challan=challan,
                          subtotal=subtotal,
                          tax_amount=tax_amount,
                          total_amount=total_amount,
                          tax_rate=tax_rate)

@orders_enhanced_bp.route('/generate-invoice/<order_id>', methods=['POST'])
@login_required
def generate_invoice(order_id):
    """Generate professional invoice for order"""
    db = get_db()

    try:
        # Check if order exists
        order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
        if not order:
            return jsonify({'success': False, 'error': 'Order not found'})

        # Check if invoice already exists
        existing_invoice = db.execute('SELECT * FROM invoices WHERE order_id = ?', (order_id,)).fetchone()
        if existing_invoice:
            return jsonify({'success': False, 'error': 'Invoice already exists for this order'})

        # Generate invoice number
        last_invoice = db.execute('SELECT invoice_number FROM invoices ORDER BY id DESC LIMIT 1').fetchone()
        if last_invoice:
            try:
                num = int(last_invoice['invoice_number'].split('-')[-1])
                next_num = num + 1
            except:
                next_num = 1
        else:
            next_num = 1

        invoice_number = f"INV-{datetime.now().strftime('%Y')}-{next_num:04d}"

        # Get order items for calculation
        order_items = db.execute('''
            SELECT * FROM order_items WHERE order_id = ?
        ''', (order_id,)).fetchall()

        # Calculate totals
        subtotal = sum(float(item['line_total'] or 0) for item in order_items)
        tax_amount = subtotal * 0.18  # 18% GST
        total_amount = subtotal + tax_amount

        # Create invoice
        db.execute('''
            INSERT INTO invoices (invoice_number, order_id, subtotal, tax_amount, total_amount, generated_by)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (invoice_number, order_id, subtotal, tax_amount, total_amount, session.get('username', 'system')))

        # Update order with invoice number
        db.execute('''
            UPDATE orders SET invoice_number = ?, last_updated = CURRENT_TIMESTAMP
            WHERE order_id = ?
        ''', (invoice_number, order_id))

        # Log activity
        db.execute('''
            INSERT INTO activity_logs (username, action, entity_id, details, module)
            VALUES (?, ?, ?, ?, ?)
        ''', (session.get('username', 'system'), 'GENERATE_INVOICE', order_id,
              f'Invoice {invoice_number} generated', 'orders'))

        db.commit()
        return jsonify({'success': True, 'invoice_number': invoice_number})

    except Exception as e:
        db.rollback()
        return jsonify({'success': False, 'error': str(e)})

@orders_enhanced_bp.route('/generate-challan/<order_id>', methods=['POST'])
@login_required
def generate_challan(order_id):
    """Generate delivery challan for order"""
    db = get_db()

    try:
        # Check if order exists and has invoice
        order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
        if not order:
            return jsonify({'success': False, 'error': 'Order not found'})

        if not order['invoice_number']:
            return jsonify({'success': False, 'error': 'Invoice must be generated before challan'})

        # Check if challan already exists
        existing_challan = db.execute('SELECT * FROM challans WHERE order_id = ?', (order_id,)).fetchone()
        if existing_challan:
            return jsonify({'success': False, 'error': 'Challan already exists for this order'})

        # Generate DC number
        last_challan = db.execute('SELECT dc_number FROM challans ORDER BY id DESC LIMIT 1').fetchone()
        if last_challan:
            try:
                num = int(last_challan['dc_number'].split('-')[-1])
                next_num = num + 1
            except:
                next_num = 1
        else:
            next_num = 1

        dc_number = f"DC-{datetime.now().strftime('%Y')}-{next_num:04d}"

        # Create challan
        db.execute('''
            INSERT INTO challans (dc_number, invoice_number, order_id, generated_by)
            VALUES (?, ?, ?, ?)
        ''', (dc_number, order['invoice_number'], order_id, session.get('username', 'system')))

        # Log activity
        db.execute('''
            INSERT INTO activity_logs (username, action, entity_id, details, module)
            VALUES (?, ?, ?, ?, ?)
        ''', (session.get('username', 'system'), 'GENERATE_CHALLAN', order_id,
              f'Delivery challan {dc_number} generated', 'orders'))

        db.commit()
        return jsonify({'success': True, 'dc_number': dc_number})

    except Exception as e:
        db.rollback()
        return jsonify({'success': False, 'error': str(e)})

@orders_enhanced_bp.route('/dispatch/<order_id>')
@login_required
def dispatch_management(order_id):
    """Professional Dispatch Management"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found.', 'danger')
        return redirect(url_for('orders.index'))

    try:
        # Get order items
        order_items = db.execute('''
            SELECT oi.*, p.name as product_name, p.strength, i.batch_number, i.expiry_date
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            LEFT JOIN inventory i ON oi.product_id = i.product_id
            WHERE oi.order_id = ?
        ''', (order_id,)).fetchall()

        # Get available riders
        riders = db.execute('''
            SELECT rider_id, name, phone, current_location, status
            FROM riders
            WHERE status = 'active'
            ORDER BY name
        ''').fetchall()

        # Get dispatch history
        dispatch_logs = db.execute('''
            SELECT * FROM activity_logs
            WHERE entity_id = ? AND action LIKE '%DISPATCH%'
            ORDER BY timestamp DESC
        ''', (order_id,)).fetchall()

    except Exception as e:
        flash(f'Error loading dispatch data: {str(e)}', 'warning')
        order_items = []
        riders = []
        dispatch_logs = []

    return render_template('orders/dispatch_management.html',
                          order=order,
                          order_items=order_items,
                          riders=riders,
                          dispatch_logs=dispatch_logs,
                          statuses=ORDER_STATUSES)

@orders_enhanced_bp.route('/dispatch/<order_id>/assign', methods=['POST'])
@login_required
def assign_dispatch(order_id):
    """Assign order to rider for dispatch"""
    db = get_db()

    try:
        rider_id = request.form.get('rider_id')
        dispatch_notes = request.form.get('dispatch_notes', '').strip()

        if not rider_id:
            flash('Please select a rider for dispatch.', 'danger')
            return redirect(url_for('orders_enhanced.dispatch_management', order_id=order_id))

        # Update order status and assign rider
        db.execute('''
            UPDATE orders SET
                status = 'Dispatched',
                rider_id = ?,
                dispatch_date = CURRENT_TIMESTAMP,
                notes = ?,
                last_updated = CURRENT_TIMESTAMP,
                updated_by = ?
            WHERE order_id = ?
        ''', (rider_id, dispatch_notes, session.get('username', 'system'), order_id))

        # Log dispatch activity
        db.execute('''
            INSERT INTO activity_logs (username, action, entity_id, details, module)
            VALUES (?, ?, ?, ?, ?)
        ''', (session.get('username', 'system'), 'DISPATCH_ORDER', order_id,
              f'Order dispatched to rider {rider_id}. Notes: {dispatch_notes}', 'orders'))

        db.commit()
        flash('Order successfully dispatched!', 'success')
        return redirect(url_for('orders.view_order', order_id=order_id))

    except Exception as e:
        db.rollback()
        flash(f'Error dispatching order: {str(e)}', 'danger')
        return redirect(url_for('orders_enhanced.dispatch_management', order_id=order_id))

@orders_enhanced_bp.route('/delivery/<order_id>')
@login_required
def delivery_management(order_id):
    """Professional Delivery Management and Tracking"""
    db = get_db()

    # Get order details
    order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
    if not order:
        flash(f'Order {order_id} not found.', 'danger')
        return redirect(url_for('orders.index'))

    try:
        # Get rider details if assigned
        rider = None
        if order['rider_id']:
            rider = db.execute('SELECT * FROM riders WHERE rider_id = ?', (order['rider_id'],)).fetchone()

        # Get delivery tracking logs
        delivery_logs = db.execute('''
            SELECT * FROM activity_logs
            WHERE entity_id = ? AND (action LIKE '%DELIVERY%' OR action LIKE '%DISPATCH%')
            ORDER BY timestamp DESC
        ''', (order_id,)).fetchall()

        # Get order items for delivery confirmation
        order_items = db.execute('''
            SELECT oi.*, p.name as product_name
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            WHERE oi.order_id = ?
        ''', (order_id,)).fetchall()

    except Exception as e:
        flash(f'Error loading delivery data: {str(e)}', 'warning')
        rider = None
        delivery_logs = []
        order_items = []

    return render_template('orders/delivery_management.html',
                          order=order,
                          rider=rider,
                          delivery_logs=delivery_logs,
                          order_items=order_items,
                          statuses=ORDER_STATUSES)

@orders_enhanced_bp.route('/delivery/<order_id>/confirm', methods=['POST'])
@login_required
def confirm_delivery(order_id):
    """Confirm order delivery"""
    db = get_db()

    try:
        delivery_notes = request.form.get('delivery_notes', '').strip()
        customer_signature = request.form.get('customer_signature', '').strip()

        # Update order status to delivered
        db.execute('''
            UPDATE orders SET
                status = 'Delivered',
                delivery_date = CURRENT_TIMESTAMP,
                notes = ?,
                last_updated = CURRENT_TIMESTAMP,
                updated_by = ?
            WHERE order_id = ?
        ''', (delivery_notes, session.get('username', 'system'), order_id))

        # Log delivery confirmation
        db.execute('''
            INSERT INTO activity_logs (username, action, entity_id, details, module)
            VALUES (?, ?, ?, ?, ?)
        ''', (session.get('username', 'system'), 'CONFIRM_DELIVERY', order_id,
              f'Order delivery confirmed. Notes: {delivery_notes}', 'orders'))

        db.commit()
        flash('Order delivery confirmed successfully!', 'success')
        return redirect(url_for('orders.view_order', order_id=order_id))

    except Exception as e:
        db.rollback()
        flash(f'Error confirming delivery: {str(e)}', 'danger')
        return redirect(url_for('orders_enhanced.delivery_management', order_id=order_id))
