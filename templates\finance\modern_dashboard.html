{% extends "base.html" %}

{% block title %}Finance Dashboard - Medivent ERP{% endblock %}

{% block content %}
<style>
    /* Modern Finance Dashboard Styles - Matching Reference Image */
    .finance-dashboard {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
    }

    /* 2025 Modern Statistics Cards with Glassmorphism */
    .stats-row {
        margin-bottom: 40px;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        padding: 32px;
        margin-bottom: 24px;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.1),
            0 2px 16px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        height: 160px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient);
        border-radius: 24px 24px 0 0;
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.15),
            0 8px 32px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
        border-color: rgba(255, 255, 255, 0.5);
    }

    .stat-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .stat-icon {
        width: 64px;
        height: 64px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        background: var(--icon-gradient);
        box-shadow:
            0 8px 24px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
    }

    .stat-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        transition: all 0.6s ease;
        opacity: 0;
    }

    .stat-card:hover .stat-icon::before {
        opacity: 1;
        animation: shimmer 1.5s ease-in-out;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .stat-trend {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.85rem;
        font-weight: 600;
        padding: 6px 12px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
    }

    .stat-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #2c3e50, #34495e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 8px;
        line-height: 1;
        letter-spacing: -0.02em;
    }

    .stat-label {
        color: #64748b;
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .stat-sublabel {
        color: #94a3b8;
        font-size: 0.8rem;
        font-weight: 500;
        margin-top: 4px;
    }

    /* 2025 Modern Icon Gradients */
    .stat-card.revenue {
        --card-gradient: linear-gradient(135deg, #10b981, #059669);
        --icon-gradient: linear-gradient(135deg, #10b981, #059669, #047857);
    }

    .stat-card.monthly {
        --card-gradient: linear-gradient(135deg, #3b82f6, #2563eb);
        --icon-gradient: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
    }

    .stat-card.pending {
        --card-gradient: linear-gradient(135deg, #f59e0b, #d97706);
        --icon-gradient: linear-gradient(135deg, #f59e0b, #d97706, #b45309);
    }

    .stat-card.customers {
        --card-gradient: linear-gradient(135deg, #8b5cf6, #7c3aed);
        --icon-gradient: linear-gradient(135deg, #8b5cf6, #7c3aed, #6d28d9);
    }

    .stat-card.profit {
        --card-gradient: linear-gradient(135deg, #06b6d4, #0891b2);
        --icon-gradient: linear-gradient(135deg, #06b6d4, #0891b2, #0e7490);
    }

    .stat-card.growth {
        --card-gradient: linear-gradient(135deg, #ec4899, #db2777);
        --icon-gradient: linear-gradient(135deg, #ec4899, #db2777, #be185d);
    }
    .stat-icon.orders { background: linear-gradient(135deg, #e67e22, #f39c12); }

    /* Navigation Grid */
    .nav-grid {
        margin-bottom: 30px;
    }

    .nav-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-bottom: 20px;
        text-decoration: none;
        color: inherit;
    }

    .nav-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        text-decoration: none;
        color: inherit;
    }

    .nav-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: white;
        margin: 0 auto 10px;
    }

    .nav-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        line-height: 1.2;
    }

    .nav-subtitle {
        font-size: 0.75rem;
        color: #7f8c8d;
        margin: 5px 0 0;
        line-height: 1.2;
    }

    /* Navigation Icon Colors */
    .nav-icon.pending-invoices { background: linear-gradient(135deg, #e74c3c, #c0392b); }
    .nav-icon.customer-ledger { background: linear-gradient(135deg, #1abc9c, #16a085); }
    .nav-icon.payment-collection { background: linear-gradient(135deg, #3498db, #2980b9); }
    .nav-icon.financial-reports { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
    .nav-icon.accounts-receivable { background: linear-gradient(135deg, #34495e, #2c3e50); }
    .nav-icon.accounts-payable { background: linear-gradient(135deg, #e91e63, #ad1457); }
    .nav-icon.duplicate-detection { background: linear-gradient(135deg, #ff9800, #f57c00); }
    .nav-icon.payment-knockoff { background: linear-gradient(135deg, #795548, #5d4037); }
    .nav-icon.salesperson-ledger { background: linear-gradient(135deg, #607d8b, #455a64); }
    .nav-icon.division-ledger { background: linear-gradient(135deg, #4caf50, #388e3c); }
    .nav-icon.comprehensive-report { background: linear-gradient(135deg, #673ab7, #512da8); }
    .nav-icon.order-workflow { background: linear-gradient(135deg, #ff5722, #d84315); }

    /* Recent Orders Section */
    .recent-orders {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-top: 30px;
    }

    .recent-orders h3 {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .recent-orders h3 i {
        margin-right: 10px;
        color: #3498db;
    }

    .order-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #ecf0f1;
    }

    .order-item:last-child {
        border-bottom: none;
    }

    .order-info h5 {
        color: #2c3e50;
        font-size: 0.95rem;
        font-weight: 600;
        margin-bottom: 3px;
    }

    .order-info p {
        color: #7f8c8d;
        font-size: 0.8rem;
        margin: 0;
    }

    .order-amount {
        font-size: 1rem;
        font-weight: 700;
        color: #27ae60;
        text-align: right;
    }

    .status-badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 500;
        margin-top: 3px;
        display: inline-block;
    }

    .status-paid {
        background: #d5f4e6;
        color: #27ae60;
    }

    .status-pending {
        background: #fef9e7;
        color: #f39c12;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stat-value {
            font-size: 1.5rem;
        }

        .nav-card {
            padding: 15px;
            height: 100px;
        }

        .nav-icon {
            width: 35px;
            height: 35px;
            font-size: 14px;
        }

        .nav-title {
            font-size: 0.8rem;
        }

        .nav-subtitle {
            font-size: 0.7rem;
        }
    }
</style>

<div class="finance-dashboard">
    <div class="dashboard-container">
        <!-- 2025 Modern Statistics Cards -->
        <div class="row stats-row">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card revenue">
                    <div class="stat-card-header">
                        <div class="stat-icon">
                            <i class="fas fa-rupee-sign"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">+12.5%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">₹{{ "{:,.0f}".format(stats.total_revenue) }}</div>
                        <div class="stat-label">Total Revenue</div>
                        <div class="stat-sublabel">All-time earnings</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card monthly">
                    <div class="stat-card-header">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-month"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">+8.3%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">₹{{ "{:,.0f}".format(stats.monthly_revenue) }}</div>
                        <div class="stat-label">This Month</div>
                        <div class="stat-sublabel">Current month revenue</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card pending">
                    <div class="stat-card-header">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-down text-warning"></i>
                            <span class="text-warning">-5.2%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">₹{{ "{:,.0f}".format(stats.pending_payments) }}</div>
                        <div class="stat-label">Pending Payments</div>
                        <div class="stat-sublabel">Outstanding amounts</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card customers">
                    <div class="stat-card-header">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">+15.7%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.total_customers or 0 }}</div>
                        <div class="stat-label">Active Customers</div>
                        <div class="stat-sublabel">Total customer base</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Modern KPI Row -->
        <div class="row stats-row">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card profit">
                    <div class="stat-card-header">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">+22.1%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">₹{{ "{:,.0f}".format((stats.total_revenue or 0) * 0.25) }}</div>
                        <div class="stat-label">Profit Margin</div>
                        <div class="stat-sublabel">Estimated profit</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card growth">
                    <div class="stat-card-header">
                        <div class="stat-icon">
                            <i class="fas fa-trending-up"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">+18.9%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.paid_orders or 0 }}</div>
                        <div class="stat-label">Completed Orders</div>
                        <div class="stat-sublabel">Successfully processed</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card revenue">
                    <div class="stat-card-header">
                        <div class="stat-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">+9.4%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">₹{{ "{:,.0f}".format((stats.total_revenue or 0) / 7) }}</div>
                        <div class="stat-label">Daily Average</div>
                        <div class="stat-sublabel">Average daily revenue</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card monthly">
                    <div class="stat-card-header">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success"></i>
                            <span class="text-success">+3.2%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ "{:.1f}%".format(((stats.paid_orders or 0) / ((stats.total_orders or 1) if (stats.total_orders or 1) > 1 else 1)) * 100) }}</div>
                        <div class="stat-label">Success Rate</div>
                        <div class="stat-sublabel">Payment completion</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Grid -->
        <div class="row nav-grid">
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('finance_pending_invoices_management') }}" class="nav-card">
                    <div class="nav-icon pending-invoices">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="nav-title">Pending Invoices</div>
                    <div class="nav-subtitle">Manage and track pending invoices</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('finance_customer_ledger') }}" class="nav-card">
                    <div class="nav-icon customer-ledger">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="nav-title">Customer Ledger</div>
                    <div class="nav-subtitle">Customer account statements</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('finance_payment_collection') }}" class="nav-card">
                    <div class="nav-icon payment-collection">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="nav-title">Payment Collection</div>
                    <div class="nav-subtitle">Collect and process payments</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('financial_reports') }}" class="nav-card">
                    <div class="nav-icon financial-reports">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="nav-title">Financial Reports</div>
                    <div class="nav-subtitle">Generate financial reports</div>
                </a>
            </div>
        </div>

        <div class="row nav-grid">
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('accounts_receivable') }}" class="nav-card">
                    <div class="nav-icon accounts-receivable">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="nav-title">Accounts Receivable</div>
                    <div class="nav-subtitle">Manage outstanding receivables</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('accounts_payable') }}" class="nav-card">
                    <div class="nav-icon accounts-payable">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="nav-title">Accounts Payable</div>
                    <div class="nav-subtitle">Manage outstanding payables</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="#" onclick="openPaymentKnockoffModal()" class="nav-card">
                    <div class="nav-icon payment-knockoff">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="nav-title">Payment Knock-off</div>
                    <div class="nav-subtitle">Match payments to invoices</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('duplicate_order_detection') }}" class="nav-card">
                    <div class="nav-icon duplicate-detection">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="nav-title">Duplicate Detection</div>
                    <div class="nav-subtitle">Detect duplicate entries</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="#" class="nav-card">
                    <div class="nav-icon payment-knockoff">
                        <i class="fas fa-check-double"></i>
                    </div>
                    <div class="nav-title">Payment Knock-off</div>
                    <div class="nav-subtitle">Knock off pending payments</div>
                </a>
            </div>
        </div>

        <div class="row nav-grid">
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('salesperson_ledger') }}" class="nav-card">
                    <div class="nav-icon salesperson-ledger">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="nav-title">Salesperson Ledger</div>
                    <div class="nav-subtitle">Sales team performance</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('division_ledger') }}" class="nav-card">
                    <div class="nav-icon division-ledger">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="nav-title">Division Ledger</div>
                    <div class="nav-subtitle">Division-wise analysis</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('comprehensive_finance_reports') }}" class="nav-card">
                    <div class="nav-icon comprehensive-report">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="nav-title">Comprehensive Report</div>
                    <div class="nav-subtitle">Detailed financial analysis</div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a href="{{ url_for('advanced_financial_analytics') }}" class="nav-card">
                    <div class="nav-icon order-workflow">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="nav-title">Advanced Analytics</div>
                    <div class="nav-subtitle">AI-powered financial insights</div>
                </a>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="row">
            <div class="col-12">
                <div class="recent-orders">
                    <h3>
                        <i class="fas fa-history"></i>Recent Orders
                    </h3>

                    {% if stats.recent_orders %}
                        {% for order in stats.recent_orders %}
                        <div class="order-item">
                            <div class="order-info">
                                <h5>{{ order.customer_name }}</h5>
                                <p>{{ order.order_id }} • {{ order.order_date }}</p>
                            </div>
                            <div class="order-amount">
                                ₹{{ "{:,.0f}".format(order.order_amount) }}
                                <div class="status-badge {% if order.payment_status == 'paid' %}status-paid{% else %}status-pending{% endif %}">
                                    {{ order.payment_status.title() }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No recent orders found</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh stats every 30 seconds
setInterval(function() {
    fetch('{{ url_for("finance_api_stats") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update stats without page reload
                console.log('Stats refreshed');
            }
        })
        .catch(error => console.log('Auto-refresh error:', error));
}, 30000);

// Payment Knock-off Modal Functions
function openPaymentKnockoffModal() {
    $('#paymentKnockoffModal').modal('show');
    loadAvailablePayments();
    loadOutstandingInvoices();
}

function loadAvailablePayments() {
    fetch('/finance/api/available-payments')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('knockoffPaymentSelect');
                select.innerHTML = '<option value="">Select Payment...</option>';
                data.payments.forEach(payment => {
                    const option = document.createElement('option');
                    option.value = payment.payment_id;
                    option.textContent = `${payment.payment_id} - ₹${payment.remaining_amount.toLocaleString()} (${payment.customer_name})`;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading payments:', error));
}

function loadOutstandingInvoices() {
    fetch('/finance/api/outstanding-invoices')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('knockoffInvoiceSelect');
                select.innerHTML = '<option value="">Select Invoice...</option>';
                data.invoices.forEach(invoice => {
                    const option = document.createElement('option');
                    option.value = invoice.invoice_id;
                    option.textContent = `${invoice.invoice_number} - ₹${invoice.outstanding_amount.toLocaleString()} (${invoice.customer_name})`;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading invoices:', error));
}

function processKnockoff() {
    const paymentId = document.getElementById('knockoffPaymentSelect').value;
    const invoiceId = document.getElementById('knockoffInvoiceSelect').value;
    const amount = parseFloat(document.getElementById('knockoffAmount').value);
    const notes = document.getElementById('knockoffNotes').value;

    if (!paymentId || !invoiceId || !amount || amount <= 0) {
        alert('Please fill all required fields with valid values.');
        return;
    }

    fetch('/finance/api/payment-knockoff', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            payment_id: paymentId,
            invoice_id: invoiceId,
            knockoff_amount: amount,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Payment knock-off successful! ${data.message}`);
            $('#paymentKnockoffModal').modal('hide');
            // Refresh the page to show updated data
            location.reload();
        } else {
            alert(`Error: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error processing payment knock-off. Please try again.');
    });
}
</script>

<!-- Payment Knock-off Modal -->
<div class="modal fade" id="paymentKnockoffModal" tabindex="-1" aria-labelledby="paymentKnockoffModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="paymentKnockoffModalLabel">
                    <i class="fas fa-exchange-alt me-2"></i>Payment Knock-off
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Payment Knock-off</strong> allows you to match received payments against outstanding invoices.
                    This helps in reconciling accounts and tracking which payments cover which invoices.
                </div>

                <form id="knockoffForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="knockoffPaymentSelect" class="form-label">
                                    <i class="fas fa-credit-card me-1"></i>Available Payment
                                </label>
                                <select class="form-select" id="knockoffPaymentSelect" required>
                                    <option value="">Loading payments...</option>
                                </select>
                                <div class="form-text">Select a payment with remaining balance</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="knockoffInvoiceSelect" class="form-label">
                                    <i class="fas fa-file-invoice me-1"></i>Outstanding Invoice
                                </label>
                                <select class="form-select" id="knockoffInvoiceSelect" required>
                                    <option value="">Loading invoices...</option>
                                </select>
                                <div class="form-text">Select an invoice with outstanding amount</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="knockoffAmount" class="form-label">
                                    <i class="fas fa-rupee-sign me-1"></i>Knock-off Amount
                                </label>
                                <input type="number" class="form-control" id="knockoffAmount"
                                       placeholder="Enter amount to knock-off" step="0.01" min="0.01" required>
                                <div class="form-text">Amount to allocate from payment to invoice</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="knockoffNotes" class="form-label">
                                    <i class="fas fa-sticky-note me-1"></i>Notes (Optional)
                                </label>
                                <input type="text" class="form-control" id="knockoffNotes"
                                       placeholder="Add notes for this knock-off">
                                <div class="form-text">Additional information about this allocation</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" onclick="processKnockoff()">
                    <i class="fas fa-check me-1"></i>Process Knock-off
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
