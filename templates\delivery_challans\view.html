{% extends "base.html" %}

{% block title %}Delivery Challan - {{ challan.dc_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-truck"></i> Delivery Challan - {{ challan.dc_number }}
        </h1>
        <div>
            <a href="{{ url_for('dc_generation.download_dc_pdf', dc_number=challan.dc_number) }}"
               class="btn btn-success">
                <i class="fas fa-file-pdf"></i> Download PDF
            </a>
            <a href="{{ url_for('delivery_challans') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to DCs
            </a>
        </div>
    </div>

    <!-- <PERSON><PERSON> Information -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-primary text-white">
            <h6 class="m-0 font-weight-bold">Delivery Challan Details</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>DC Number:</strong></td>
                            <td class="text-primary">{{ challan.dc_number }}</td>
                        </tr>
                        <tr>
                            <td><strong>Order ID:</strong></td>
                            <td>
                                <a href="{{ url_for('view_order', order_id=challan.order_id) }}">
                                    {{ challan.order_id }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Customer:</strong></td>
                            <td>{{ challan.customer_name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Order Date:</strong></td>
                            <td>{{ safe_strftime(challan.order_date, '%Y-%m-%d %H:%M') if challan.order_date else 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Generated Date:</strong></td>
                            <td>{{ safe_strftime(challan.created_date, '%Y-%m-%d %H:%M') if challan.created_date else 'N/A' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                {% if challan.status == 'created' %}
                                    <span class="badge badge-primary">Created</span>
                                {% elif challan.status == 'dispatched' %}
                                    <span class="badge badge-warning">Dispatched</span>
                                {% elif challan.status == 'delivered' %}
                                    <span class="badge badge-success">Delivered</span>
                                {% else %}
                                    <span class="badge badge-secondary">{{ challan.status|title }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Warehouse:</strong></td>
                            <td>{{ challan.warehouse_id }}</td>
                        </tr>
                        <tr>
                            <td><strong>Created By:</strong></td>
                            <td>{{ challan.created_by or 'System' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Total Amount:</strong></td>
                            <td class="text-success"><strong>₹{{ "%.2f"|format(challan.order_amount) }}</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Total Items:</strong></td>
                            <td><span class="badge badge-info">{{ challan.total_items or 0 }}</span></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Information -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-info text-white">
            <h6 class="m-0 font-weight-bold">Customer Information</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <strong>Name:</strong><br>
                    {{ challan.customer_name }}
                </div>
                <div class="col-md-6">
                    <strong>Phone:</strong><br>
                    {{ challan.customer_phone or 'N/A' }}
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <strong>Address:</strong><br>
                    {{ challan.customer_address or 'N/A' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Batch Selections -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-success text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-boxes"></i> Batch Allocations
            </h6>
        </div>
        <div class="card-body">
            {% if batch_selections %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th>Product</th>
                            <th>Batch Number</th>
                            <th>Warehouse</th>
                            <th>Allocated Quantity</th>
                            <th>Selection Method</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for selection in batch_selections %}
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ selection.product_name }}</strong>
                                    {% if selection.strength %}
                                    <br><small class="text-muted">{{ selection.strength }}</small>
                                    {% endif %}
                                    <br><small class="text-muted">ID: {{ selection.product_id }}</small>
                                </div>
                            </td>
                            <td>
                                <code class="text-monospace">{{ selection.batch_number }}</code>
                            </td>
                            <td>
                                <span class="badge badge-secondary">{{ selection.warehouse_name }}</span>
                            </td>
                            <td>
                                <span class="badge badge-primary">{{ selection.allocated_quantity }}</span>
                            </td>
                            <td>
                                {% if selection.selection_method == 'fifo' %}
                                    <span class="badge badge-info">FIFO</span>
                                {% elif selection.selection_method == 'manual' %}
                                    <span class="badge badge-warning">Manual</span>
                                {% else %}
                                    <span class="badge badge-secondary">{{ selection.selection_method|title }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ safe_strftime(selection.created_at, '%Y-%m-%d %H:%M') if selection.created_at else 'N/A' }}
                                </small>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Batch Summary -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <h6>Batch Summary:</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card border-left-primary">
                                <div class="card-body py-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Batches
                                    </div>
                                    <div class="h6 mb-0 font-weight-bold text-gray-800">
                                        {{ batch_selections|length }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-left-info">
                                <div class="card-body py-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        FIFO Allocations
                                    </div>
                                    <div class="h6 mb-0 font-weight-bold text-gray-800">
                                        {{ batch_selections|selectattr('selection_method', 'equalto', 'fifo')|list|length }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-left-warning">
                                <div class="card-body py-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Manual Allocations
                                    </div>
                                    <div class="h6 mb-0 font-weight-bold text-gray-800">
                                        {{ batch_selections|selectattr('selection_method', 'equalto', 'manual')|list|length }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-left-success">
                                <div class="card-body py-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Total Quantity
                                    </div>
                                    <div class="h6 mb-0 font-weight-bold text-gray-800">
                                        {{ batch_selections|sum(attribute='allocated_quantity') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                <h6 class="text-muted">No Batch Information Available</h6>
                <p class="text-muted">This delivery challan was generated without batch selection details.</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Status Update Actions -->
    {% if challan.status in ['created', 'dispatched'] %}
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-warning text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-edit"></i> Update Status
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <p class="mb-0">Update the delivery status of this challan:</p>
                </div>
                <div class="col-md-4 text-right">
                    {% if challan.status == 'created' %}
                    <button class="btn btn-warning" onclick="updateStatus('{{ challan.dc_number }}', 'dispatched')">
                        <i class="fas fa-shipping-fast"></i> Mark as Dispatched
                    </button>
                    {% elif challan.status == 'dispatched' %}
                    <button class="btn btn-success" onclick="updateStatus('{{ challan.dc_number }}', 'delivered')">
                        <i class="fas fa-check-circle"></i> Mark as Delivered
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Notes Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-secondary text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-sticky-note"></i> Notes
            </h6>
        </div>
        <div class="card-body">
            {% if challan.notes %}
                <p>{{ challan.notes }}</p>
            {% else %}
                <p class="text-muted">No notes available for this delivery challan.</p>
            {% endif %}
        </div>
    </div>
</div>

<script>
function updateStatus(dcNumber, newStatus) {
    const statusText = newStatus === 'dispatched' ? 'Dispatched' : 'Delivered';
    
    if (confirm(`Mark DC ${dcNumber} as ${statusText}?`)) {
        // Create a form to submit the status update
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/delivery_challans/${dcNumber}/update-status`;
        
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = newStatus;
        
        form.appendChild(statusInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
