#!/usr/bin/env python3
"""
DC Generation Models - Clean Implementation
Handles all DC generation logic with proper error handling
"""

import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Database helper function with proper connection management
def get_db():
    conn = sqlite3.connect('instance/medivent.db', timeout=30.0)
    conn.row_factory = sqlite3.Row
    conn.execute("PRAGMA foreign_keys = ON")
    conn.execute("PRAGMA journal_mode = WAL")  # Better concurrency
    return conn


class DCGenerationError(Exception):
    """Custom exception for DC generation errors"""
    pass


class InventoryManager:
    """Handles inventory operations for DC generation"""
    
    @staticmethod
    def get_available_inventory(product_id: str, warehouse_id: str = None) -> List[Dict]:
        """Get available inventory for a product, optionally filtered by warehouse"""
        try:
            db = get_db()
            
            query = '''
                SELECT i.*, w.name as warehouse_name,
                       (i.stock_quantity - COALESCE(i.allocated_quantity, 0)) as available_quantity
                FROM inventory i
                JOIN warehouses w ON i.warehouse_id = w.warehouse_id
                WHERE i.product_id = ? 
                  AND i.status = 'active'
                  AND (i.stock_quantity - COALESCE(i.allocated_quantity, 0)) > 0
            '''
            params = [product_id]
            
            if warehouse_id:
                query += ' AND i.warehouse_id = ?'
                params.append(warehouse_id)
            
            query += ' ORDER BY i.manufacturing_date ASC, i.expiry_date ASC'
            
            inventory = db.execute(query, params).fetchall()
            return [dict(row) for row in inventory]
            
        except Exception as e:
            raise DCGenerationError(f"Error fetching inventory: {str(e)}")
    
    @staticmethod
    def allocate_inventory(inventory_id: str, quantity: int, reference_type: str, reference_id: str) -> bool:
        """Allocate inventory for DC generation with proper transaction management"""
        db = None
        try:
            db = get_db()

            # Start transaction
            db.execute('BEGIN IMMEDIATE')

            # Check current availability
            current = db.execute(
                'SELECT stock_quantity, allocated_quantity FROM inventory WHERE inventory_id = ?',
                (inventory_id,)
            ).fetchone()

            if not current:
                raise DCGenerationError(f"Inventory {inventory_id} not found")

            available = current['stock_quantity'] - (current['allocated_quantity'] or 0)
            if available < quantity:
                raise DCGenerationError(f"Insufficient stock. Available: {available}, Requested: {quantity}")

            # Update allocation
            new_allocated = (current['allocated_quantity'] or 0) + quantity
            db.execute(
                'UPDATE inventory SET allocated_quantity = ? WHERE inventory_id = ?',
                (new_allocated, inventory_id)
            )

            # Record stock movement
            db.execute('''
                INSERT INTO stock_movements
                (inventory_id, movement_type, quantity, reference_type, reference_id, created_at)
                VALUES (?, 'allocation', ?, ?, ?, ?)
            ''', (inventory_id, quantity, reference_type, reference_id, datetime.now()))

            # Commit transaction
            db.commit()
            return True

        except Exception as e:
            if db:
                db.rollback()
            raise DCGenerationError(f"Error allocating inventory: {str(e)}")
        finally:
            if db:
                db.close()
    
    @staticmethod
    def deallocate_inventory(inventory_id: str, quantity: int, reference_type: str, reference_id: str) -> bool:
        """Deallocate inventory (rollback allocation)"""
        try:
            db = get_db()
            
            # Get current allocation
            current = db.execute(
                'SELECT allocated_quantity FROM inventory WHERE inventory_id = ?',
                (inventory_id,)
            ).fetchone()
            
            if not current:
                raise DCGenerationError(f"Inventory {inventory_id} not found")
            
            current_allocated = current['allocated_quantity'] or 0
            if current_allocated < quantity:
                raise DCGenerationError(f"Cannot deallocate more than allocated. Current: {current_allocated}")
            
            # Update allocation
            new_allocated = current_allocated - quantity
            db.execute(
                'UPDATE inventory SET allocated_quantity = ? WHERE inventory_id = ?',
                (new_allocated, inventory_id)
            )
            
            # Record stock movement
            db.execute('''
                INSERT INTO stock_movements 
                (inventory_id, movement_type, quantity, reference_type, reference_id, created_at)
                VALUES (?, 'deallocation', ?, ?, ?, ?)
            ''', (inventory_id, quantity, reference_type, reference_id, datetime.now()))
            
            return True
            
        except Exception as e:
            raise DCGenerationError(f"Error deallocating inventory: {str(e)}")


class BatchSelector:
    """Handles batch selection logic"""
    
    @staticmethod
    def get_order_inventory_data(order_id: str) -> Dict:
        """Get formatted inventory data for order items"""
        try:
            db = get_db()
            
            # Get order items
            order_items = db.execute('''
                SELECT oi.*, p.name as product_name, p.strength
                FROM order_items oi
                JOIN products p ON oi.product_id = p.product_id
                WHERE oi.order_id = ?
                ORDER BY p.name
            ''', (order_id,)).fetchall()
            
            if not order_items:
                raise DCGenerationError(f"No items found for order {order_id}")
            
            # Get warehouses
            warehouses = db.execute('SELECT * FROM warehouses ORDER BY name').fetchall()
            
            inventory_data = {}
            
            for item in order_items:
                product_id = item['product_id']
                inventory_data[product_id] = {
                    'product_name': item['product_name'],
                    'strength': item['strength'],
                    'required_quantity': item['quantity'],
                    'warehouses': {}
                }
                
                # Get inventory for each warehouse
                for warehouse in warehouses:
                    warehouse_id = warehouse['warehouse_id']
                    
                    # Get available batches
                    batches = InventoryManager.get_available_inventory(product_id, warehouse_id)
                    
                    if batches:  # Only include warehouses with available stock
                        inventory_data[product_id]['warehouses'][warehouse_id] = {
                            'warehouse_name': warehouse['name'],
                            'batches': batches
                        }
            
            return inventory_data
            
        except Exception as e:
            raise DCGenerationError(f"Error getting inventory data: {str(e)}")
    
    @staticmethod
    def validate_batch_selections(order_id: str, selections: Dict) -> Tuple[bool, List[str]]:
        """Validate batch selections against order requirements"""
        try:
            db = get_db()
            errors = []
            
            # Get order items
            order_items = db.execute('''
                SELECT product_id, quantity as required_quantity
                FROM order_items
                WHERE order_id = ?
            ''', (order_id,)).fetchall()
            
            order_requirements = {item['product_id']: item['required_quantity'] for item in order_items}
            
            # Validate each product
            for product_id, required_qty in order_requirements.items():
                if product_id not in selections:
                    errors.append(f"No batches selected for product {product_id}")
                    continue
                
                total_selected = 0
                for batch_selection in selections[product_id]:
                    inventory_id = batch_selection.get('inventory_id')
                    quantity = batch_selection.get('quantity', 0)
                    
                    if not inventory_id or quantity <= 0:
                        continue
                    
                    # Check if inventory exists and has sufficient stock
                    inventory = db.execute('''
                        SELECT stock_quantity, allocated_quantity
                        FROM inventory
                        WHERE inventory_id = ? AND status = 'active'
                    ''', (inventory_id,)).fetchone()
                    
                    if not inventory:
                        errors.append(f"Invalid inventory ID: {inventory_id}")
                        continue
                    
                    available = inventory['stock_quantity'] - (inventory['allocated_quantity'] or 0)
                    if available < quantity:
                        errors.append(f"Insufficient stock for {inventory_id}. Available: {available}, Selected: {quantity}")
                        continue
                    
                    total_selected += quantity
                
                if total_selected < required_qty:
                    errors.append(f"Insufficient quantity selected for product {product_id}. Required: {required_qty}, Selected: {total_selected}")
                elif total_selected > required_qty:
                    errors.append(f"Excess quantity selected for product {product_id}. Required: {required_qty}, Selected: {total_selected}")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            raise DCGenerationError(f"Error validating selections: {str(e)}")


class DCGenerator:
    """Main DC generation class"""
    
    @staticmethod
    def generate_dc_number() -> str:
        """Generate sequential DC number"""
        try:
            db = get_db()
            
            # Get last DC number
            last_dc = db.execute(
                'SELECT dc_number FROM delivery_challans ORDER BY id DESC LIMIT 1'
            ).fetchone()
            
            if last_dc and last_dc['dc_number']:
                try:
                    # Extract number from DC-001 format
                    last_num = int(last_dc['dc_number'].split('-')[1])
                    next_num = last_num + 1
                except (IndexError, ValueError):
                    next_num = 1
            else:
                next_num = 1
            
            return f"DC-{next_num:03d}"
            
        except Exception as e:
            # Fallback to timestamp-based number
            return f"DC-{int(datetime.now().timestamp())}"
    
    @staticmethod
    def create_delivery_challan(order_id: str, batch_selections: Dict, created_by: str) -> str:
        """Create delivery challan with batch allocations and proper transaction management"""
        db = None
        try:
            db = get_db()

            # Start transaction with immediate lock
            db.execute('BEGIN IMMEDIATE')

            # Validate selections first
            is_valid, errors = BatchSelector.validate_batch_selections(order_id, batch_selections)
            if not is_valid:
                raise DCGenerationError(f"Validation failed: {'; '.join(errors)}")

            # Generate DC number
            dc_number = DCGenerator.generate_dc_number()

            # Get order details
            order = db.execute('SELECT * FROM orders WHERE order_id = ?', (order_id,)).fetchone()
            if not order:
                raise DCGenerationError(f"Order {order_id} not found")

            # Calculate totals
            total_items = 0
            total_amount = order['order_amount']

            # Allocate inventory (this will handle its own transactions)
            allocation_details = []
            allocated_inventories = []  # Track for rollback if needed

            try:
                for product_id, product_selections in batch_selections.items():
                    for batch_selection in product_selections:
                        inventory_id = batch_selection['inventory_id']
                        quantity = batch_selection['quantity']

                        if quantity > 0:
                            # Check availability first
                            current = db.execute(
                                'SELECT stock_quantity, allocated_quantity FROM inventory WHERE inventory_id = ?',
                                (inventory_id,)
                            ).fetchone()

                            if not current:
                                raise DCGenerationError(f"Inventory {inventory_id} not found")

                            available = current['stock_quantity'] - (current['allocated_quantity'] or 0)
                            if available < quantity:
                                raise DCGenerationError(f"Insufficient stock for {inventory_id}. Available: {available}, Requested: {quantity}")

                            # Update allocation directly in this transaction
                            new_allocated = (current['allocated_quantity'] or 0) + quantity
                            db.execute(
                                'UPDATE inventory SET allocated_quantity = ? WHERE inventory_id = ?',
                                (new_allocated, inventory_id)
                            )

                            # Record stock movement
                            db.execute('''
                                INSERT INTO stock_movements
                                (inventory_id, movement_type, quantity, reference_type, reference_id, created_at, created_by)
                                VALUES (?, 'allocation', ?, ?, ?, ?, ?)
                            ''', (inventory_id, quantity, 'dc_generation', dc_number, datetime.now(), created_by))

                            allocation_details.append({
                                'inventory_id': inventory_id,
                                'product_id': product_id,
                                'quantity': quantity
                            })

                            allocated_inventories.append((inventory_id, quantity))
                            total_items += quantity

                # Create DC record
                db.execute('''
                    INSERT INTO delivery_challans
                    (dc_number, order_id, warehouse_id, customer_name, status,
                     created_date, created_by, total_items, total_amount, batch_details)
                    VALUES (?, ?, ?, ?, 'created', ?, ?, ?, ?, ?)
                ''', (
                    dc_number, order_id, 'WH001',  # Default warehouse for now
                    order['customer_name'], datetime.now(), created_by,
                    total_items, total_amount, json.dumps(allocation_details)
                ))

                # Update order status to Finance Pending after DC generation
                db.execute('''
                    UPDATE orders
                    SET status = 'Finance Pending', last_updated = ?, updated_by = ?
                    WHERE order_id = ?
                ''', (datetime.now(), created_by, order_id))

                # Commit all changes
                db.commit()

                # Generate PDF after successful DC creation
                try:
                    from utils.pdf_generator import generate_dc_pdf
                    pdf_path = generate_dc_pdf(dc_number)
                    print(f"PDF generated successfully: {pdf_path}")
                except Exception as pdf_error:
                    print(f"Warning: PDF generation failed: {pdf_error}")
                    # Don't fail the entire DC creation if PDF fails

                return dc_number

            except Exception as e:
                # Rollback all changes
                db.rollback()
                raise e

        except Exception as e:
            if db:
                db.rollback()
            raise DCGenerationError(f"Error creating DC: {str(e)}")
        finally:
            if db:
                db.close()
