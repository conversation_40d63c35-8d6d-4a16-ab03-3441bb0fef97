#!/usr/bin/env python3
"""
Inventory Management Routes
Comprehensive inventory management with product validation
"""

from flask import Blueprint, render_template, request, jsonify, g, flash, redirect, url_for
from flask_login import login_required, current_user
from utils.inventory_validator import get_inventory_validator, get_products_with_inventory_for_forms
from utils.product_validator import get_product_validator
from utils.division_validator import get_division_validator
import sqlite3
import logging
import traceback
from datetime import datetime

inventory_bp = Blueprint('inventory', __name__, template_folder='../templates')

def get_db():
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
    return db


@inventory_bp.route('/')
@login_required
def index():
    """Inventory management dashboard"""
    try:
        db = get_db()
        inventory_validator = get_inventory_validator(db)
        
        # Get inventory summary
        summary = inventory_validator.get_inventory_summary()
        
        # Get low stock products
        low_stock_products = inventory_validator.get_low_stock_products()
        
        # Get recent inventory entries
        cursor = db.execute('''
            SELECT i.inventory_id, i.batch_number, i.stock_quantity,
                   i.allocated_quantity, i.date_received, i.status,
                   p.name as product_name, p.strength,
                   d.name as division_name,
                   w.name as warehouse_name
            FROM inventory i
            JOIN products p ON i.product_id = p.product_id
            JOIN divisions d ON p.division_id = d.division_id
            LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE i.status = 'active' AND d.is_active = 1
            ORDER BY i.date_received DESC
            LIMIT 20
        ''')

        recent_inventory = cursor.fetchall()

        # Also get products without inventory for better user experience
        cursor = db.execute('''
            SELECT p.product_id, p.name as product_name, p.strength,
                   d.name as division_name,
                   COALESCE(SUM(i.stock_quantity), 0) as total_stock
            FROM products p
            JOIN divisions d ON p.division_id = d.division_id
            LEFT JOIN inventory i ON p.product_id = i.product_id AND i.status = 'active'
            WHERE d.is_active = 1
            GROUP BY p.product_id, p.name, p.strength, d.name
            ORDER BY p.name
        ''')

        products_summary = cursor.fetchall()
        
        return render_template('inventory/index.html',
                             summary=summary,
                             low_stock_products=low_stock_products,
                             recent_inventory=recent_inventory,
                             products_summary=products_summary)
        
    except Exception as e:
        flash(f'Error loading inventory dashboard: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))


@inventory_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_inventory():
    """Create new inventory entry with comprehensive validation"""
    if request.method == 'POST':
        try:
            db = get_db()
            
            # Get form data
            inventory_data = {
                'product_id': request.form.get('product_id', '').strip(),
                'batch_number': request.form.get('batch_number', '').strip(),
                'country_of_origin': request.form.get('country', '').strip(),  # Fixed field name
                'manufacturing_date': request.form.get('manufacturing_date'),
                'expiry_date': request.form.get('expiry_date'),
                'stock_quantity': request.form.get('stock_quantity'),
                'allocated_quantity': request.form.get('allocated_quantity', 0),
                'warehouse_id': request.form.get('warehouse_id', '').strip(),
                'location_code': request.form.get('location_code', '').strip()
            }
            
            # Validate inventory data
            inventory_validator = get_inventory_validator(db)
            is_valid, errors = inventory_validator.validate_inventory_creation_data(inventory_data)
            
            if not is_valid:
                for error in errors:
                    flash(error, 'danger')
                return redirect(url_for('inventory.new_inventory'))
            
            # Generate inventory ID
            inventory_id = f"INV{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            # Begin transaction
            db.execute('BEGIN TRANSACTION')
            
            # Insert inventory
            db.execute('''
                INSERT INTO inventory (
                    inventory_id, product_id, batch_number, country_of_origin,
                    manufacturing_date, expiry_date, stock_quantity, allocated_quantity,
                    warehouse_id, location_code, status, date_received, received_by,
                    last_updated, updated_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                inventory_id,
                inventory_data['product_id'],
                inventory_data['batch_number'],
                inventory_data['country_of_origin'],
                inventory_data['manufacturing_date'],
                inventory_data['expiry_date'],
                int(inventory_data['stock_quantity']),
                int(inventory_data['allocated_quantity']),
                inventory_data['warehouse_id'],
                inventory_data['location_code'],
                'active',
                datetime.now(),
                current_user.username,
                datetime.now(),
                current_user.username
            ))
            
            # Log stock movement
            movement_id = f"MOV{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            db.execute('''
                INSERT INTO stock_movements (
                    movement_id, inventory_id, product_id, batch_number,
                    quantity, movement_type, movement_date, moved_by, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                movement_id,
                inventory_id,
                inventory_data['product_id'],
                inventory_data['batch_number'],
                int(inventory_data['stock_quantity']),
                'receipt',
                datetime.now(),
                current_user.username,
                f"Initial stock receipt - {inventory_data['batch_number']}"
            ))
            
            # Commit transaction
            db.execute('COMMIT')

            # Invalidate product caches for real-time inventory updates
            try:
                from utils.product_realtime_service import invalidate_product_caches
                invalidate_product_caches(db)
            except Exception as e:
                print(f"Failed to invalidate product caches: {e}")

            flash(f'Inventory {inventory_id} created successfully!', 'success')
            return redirect(url_for('inventory.view_inventory', inventory_id=inventory_id))
            
        except Exception as e:
            # Rollback on error
            try:
                db.execute('ROLLBACK')
            except:
                pass
            flash(f'Error creating inventory: {str(e)}', 'danger')
            return redirect(url_for('inventory.new_inventory'))
    
    # GET request - show inventory form
    try:
        db = get_db()

        # Check if product_id is provided for auto-selection
        selected_product_id = request.args.get('product_id')
        selected_product = None

        # If product_id is provided, get the selected product details
        if selected_product_id:
            try:
                selected_product = db.execute('''
                    SELECT p.*, d.name as division_name
                    FROM products p
                    JOIN divisions d ON p.division_id = d.division_id
                    WHERE p.product_id = ?
                ''', (selected_product_id,)).fetchone()

                if selected_product:
                    flash(f'Auto-Selected Product: {selected_product["name"]} ({selected_product["strength"] or "N/A"}) - {selected_product["division_name"]}', 'info')
            except Exception as e:
                flash(f'Error loading selected product: {str(e)}', 'warning')
                selected_product = None

        # Get products using real-time service (for manual selection)
        try:
            from utils.product_realtime_service import get_products_for_forms_realtime
            products = get_products_for_forms_realtime(db)
        except Exception as e:
            # Fallback to product validator
            product_validator = get_product_validator(db)
            products = product_validator.get_products_for_inventory_creation()

        if not products and not selected_product:
            flash('No products with valid divisions found. Please register products first.', 'warning')
            return redirect(url_for('products.new_product'))

        # Get warehouses
        cursor = db.execute('SELECT * FROM warehouses WHERE status = "active" ORDER BY name')
        warehouses = cursor.fetchall()

        return render_template('inventory/new.html',
                             products=products,
                             warehouses=warehouses,
                             selected_product=selected_product,
                             selected_product_id=selected_product_id)

    except Exception as e:
        flash(f'Error loading inventory form: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/<inventory_id>')
@login_required
def view_inventory(inventory_id):
    """View inventory details"""
    try:
        db = get_db()
        
        # Get inventory details with product and division info
        cursor = db.execute('''
            SELECT i.*, p.name as product_name, p.strength, p.manufacturer,
                   d.name as division_name, d.category as division_category,
                   w.name as warehouse_name, w.address as warehouse_address
            FROM inventory i
            JOIN products p ON i.product_id = p.product_id
            JOIN divisions d ON p.division_id = d.division_id
            LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE i.inventory_id = ?
        ''', (inventory_id,))
        
        inventory = cursor.fetchone()
        
        if not inventory:
            flash(f'Inventory {inventory_id} not found', 'danger')
            return redirect(url_for('inventory.index'))
        
        # Get stock movements for this inventory
        cursor = db.execute('''
            SELECT * FROM stock_movements
            WHERE inventory_id = ?
            ORDER BY movement_date DESC
        ''', (inventory_id,))
        
        movements = cursor.fetchall()
        
        return render_template('inventory/view.html',
                             inventory=inventory,
                             movements=movements)

    except Exception as e:
        flash(f'Error viewing inventory: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/<inventory_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_inventory(inventory_id):
    """Edit inventory entry"""
    try:
        db = get_db()

        if request.method == 'POST':
            # Get form data
            stock_quantity = request.form.get('stock_quantity')
            allocated_quantity = request.form.get('allocated_quantity', 0)
            location_code = request.form.get('location_code', '')
            status = request.form.get('status', 'active')

            # Validate input
            if not stock_quantity or not stock_quantity.isdigit():
                flash('Valid stock quantity is required', 'danger')
                return redirect(url_for('inventory.edit_inventory', inventory_id=inventory_id))

            # Update inventory
            db.execute('''
                UPDATE inventory
                SET stock_quantity = ?, allocated_quantity = ?, location_code = ?,
                    status = ?, last_updated = ?, updated_by = ?
                WHERE inventory_id = ?
            ''', (
                int(stock_quantity),
                int(allocated_quantity) if allocated_quantity else 0,
                location_code,
                status,
                datetime.now(),
                current_user.username,
                inventory_id
            ))

            db.commit()
            flash('Inventory updated successfully', 'success')
            return redirect(url_for('inventory.view_inventory', inventory_id=inventory_id))

        # GET request - show edit form
        cursor = db.execute('''
            SELECT i.*, p.name as product_name, p.strength, p.manufacturer,
                   d.name as division_name, w.name as warehouse_name
            FROM inventory i
            JOIN products p ON i.product_id = p.product_id
            JOIN divisions d ON p.division_id = d.division_id
            LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE i.inventory_id = ?
        ''', (inventory_id,))

        inventory = cursor.fetchone()

        if not inventory:
            flash(f'Inventory {inventory_id} not found', 'danger')
            return redirect(url_for('inventory.index'))

        # Get warehouses for dropdown
        cursor = db.execute('SELECT * FROM warehouses WHERE status = "active" ORDER BY name')
        warehouses = cursor.fetchall()

        return render_template('inventory/edit.html',
                             inventory=inventory,
                             warehouses=warehouses)

    except Exception as e:
        flash(f'Error editing inventory: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/product/<product_id>')
@login_required
def product_inventory(product_id):
    """View all inventory for a specific product"""
    try:
        db = get_db()
        product_validator = get_product_validator(db)
        inventory_validator = get_inventory_validator(db)
        
        # Validate product exists and has valid division
        is_valid, product_info = product_validator.validate_product_exists(product_id)
        
        if not is_valid:
            flash(f'Product {product_id} not found', 'danger')
            return redirect(url_for('inventory.index'))
        
        if not product_info.get('division_valid', False):
            flash(f'Product {product_id} does not have a valid division assignment', 'warning')
            return redirect(url_for('inventory.index'))
        
        # Get inventory for this product
        inventory_entries = inventory_validator.get_inventory_by_product(product_id)
        
        # Get available stock
        available_stock = inventory_validator.get_available_stock(product_id)
        
        return render_template('inventory/product_inventory.html',
                             product_info=product_info,
                             inventory_entries=inventory_entries,
                             available_stock=available_stock)
        
    except Exception as e:
        flash(f'Error viewing product inventory: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/low-stock')
@login_required
def low_stock():
    """View low stock products"""
    try:
        db = get_db()
        inventory_validator = get_inventory_validator(db)
        
        # Get low stock products
        low_stock_products = inventory_validator.get_low_stock_products()
        
        return render_template('inventory/low_stock.html',
                             low_stock_products=low_stock_products)
        
    except Exception as e:
        flash(f'Error loading low stock report: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/transfer', methods=['GET', 'POST'])
@login_required
def transfer():
    """Stock Transfer function"""
    if request.method == 'POST':
        try:
            db = get_db()

            # Get form data
            inventory_id = request.form.get('inventory_id')
            to_warehouse_id = request.form.get('to_warehouse_id')
            quantity = request.form.get('quantity')

            # Validate form data
            if not inventory_id or not to_warehouse_id or not quantity:
                flash('All fields are required', 'danger')
                return redirect(url_for('inventory.transfer'))

            # Get inventory details
            cursor = db.execute('''
                SELECT i.*, p.name as product_name, w.name as warehouse_name
                FROM inventory i
                JOIN products p ON i.product_id = p.product_id
                JOIN warehouses w ON i.warehouse_id = w.warehouse_id
                WHERE i.inventory_id = ?
            ''', (inventory_id,))

            inventory = cursor.fetchone()

            if not inventory:
                flash('Inventory not found', 'danger')
                return redirect(url_for('inventory.transfer'))

            # Check if destination warehouse exists
            cursor = db.execute('SELECT * FROM warehouses WHERE warehouse_id = ?', (to_warehouse_id,))
            to_warehouse = cursor.fetchone()

            if not to_warehouse:
                flash('Destination warehouse not found', 'danger')
                return redirect(url_for('inventory.transfer'))

            # Validate quantity
            try:
                transfer_qty = int(quantity)
                if transfer_qty <= 0:
                    flash('Quantity must be greater than zero', 'danger')
                    return redirect(url_for('inventory.transfer'))

                if transfer_qty > inventory['stock_quantity']:
                    flash('Transfer quantity cannot exceed available stock', 'danger')
                    return redirect(url_for('inventory.transfer'))
            except ValueError:
                flash('Quantity must be a valid number', 'danger')
                return redirect(url_for('inventory.transfer'))

            # Check if source and destination warehouses are different
            if inventory['warehouse_id'] == to_warehouse_id:
                flash('Source and destination warehouses must be different', 'warning')
                return redirect(url_for('inventory.transfer'))

            # Perform transfer
            # 1. Reduce stock in source warehouse
            db.execute('''
                UPDATE inventory SET
                    stock_quantity = stock_quantity - ?,
                    last_updated = ?,
                    updated_by = ?
                WHERE inventory_id = ?
            ''', (transfer_qty, datetime.now(), current_user.username, inventory_id))

            # 2. Check if batch already exists in destination warehouse
            cursor = db.execute('''
                SELECT * FROM inventory
                WHERE product_id = ? AND warehouse_id = ? AND batch_number = ?
            ''', (inventory['product_id'], to_warehouse_id, inventory['batch_number']))

            existing_batch = cursor.fetchone()

            if existing_batch:
                # Update existing batch in destination
                db.execute('''
                    UPDATE inventory SET
                        stock_quantity = stock_quantity + ?,
                        last_updated = ?,
                        updated_by = ?
                    WHERE inventory_id = ?
                ''', (transfer_qty, datetime.now(), current_user.username, existing_batch['inventory_id']))
            else:
                # Create new batch in destination
                new_inventory_id = f"INV{datetime.now().strftime('%Y%m%d%H%M%S')}"
                db.execute('''
                    INSERT INTO inventory (
                        inventory_id, product_id, warehouse_id, batch_number,
                        manufacturing_date, expiry_date, stock_quantity, status,
                        date_received, received_by, last_updated, updated_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    new_inventory_id, inventory['product_id'], to_warehouse_id, inventory['batch_number'],
                    inventory['manufacturing_date'], inventory['expiry_date'], transfer_qty, inventory['status'],
                    datetime.now(), current_user.username, datetime.now(), current_user.username
                ))

            db.commit()
            flash(f'Successfully transferred {transfer_qty} units from {inventory["warehouse_name"]} to {to_warehouse["name"]}', 'success')
            return redirect(url_for('inventory.index'))

        except Exception as e:
            db.rollback()
            flash(f'Error transferring stock: {str(e)}', 'danger')
            return redirect(url_for('inventory.transfer'))

    # GET request - show the form
    try:
        db = get_db()

        # Get inventory with available stock
        cursor = db.execute('''
            SELECT i.*, p.name as product_name, p.strength, w.name as warehouse_name
            FROM inventory i
            JOIN products p ON i.product_id = p.product_id
            JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE i.stock_quantity > 0 AND i.status = 'active'
            ORDER BY p.name
        ''')
        inventory = cursor.fetchall()

        # Get warehouses
        cursor = db.execute('SELECT * FROM warehouses WHERE status = "active" ORDER BY name')
        warehouses = cursor.fetchall()

        return render_template('inventory/transfer.html',
                             inventory=inventory,
                             warehouses=warehouses)

    except Exception as e:
        flash(f'Error loading transfer form: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/<inventory_id>/history')
@login_required
def history(inventory_id):
    """View Inventory History function"""
    try:
        db = get_db()

        # Get inventory details
        cursor = db.execute('''
            SELECT i.*, p.name as product_name, p.strength, w.name as warehouse_name
            FROM inventory i
            JOIN products p ON i.product_id = p.product_id
            JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE i.inventory_id = ?
        ''', (inventory_id,))

        inventory = cursor.fetchone()

        if not inventory:
            flash('Inventory not found', 'danger')
            return redirect(url_for('inventory.index'))

        # Get movement history
        cursor = db.execute('''
            SELECT m.*,
                   w1.name as from_warehouse_name,
                   w2.name as to_warehouse_name
            FROM stock_movements m
            LEFT JOIN warehouses w1 ON m.from_warehouse_id = w1.warehouse_id
            LEFT JOIN warehouses w2 ON m.to_warehouse_id = w2.warehouse_id
            WHERE m.inventory_id = ? OR m.product_id = ?
            ORDER BY m.movement_date DESC
        ''', (inventory_id, inventory['product_id']))

        movements = cursor.fetchall()

        return render_template('inventory/history.html',
                              inventory=inventory,
                              movements=movements)

    except Exception as e:
        flash(f'Error viewing inventory history: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))


@inventory_bp.route('/api/products-for-inventory')
@login_required
def api_products_for_inventory():
    """API endpoint to get products eligible for inventory creation"""
    try:
        db = get_db()
        products = get_products_with_inventory_for_forms(db)

        return jsonify({
            'success': True,
            'products': products
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@inventory_bp.route('/api/validate-product/<product_id>')
@login_required
def api_validate_product(product_id):
    """API endpoint to validate product for inventory creation"""
    try:
        db = get_db()
        product_validator = get_product_validator(db)
        
        is_valid, product_info = product_validator.validate_product_exists(product_id)
        
        return jsonify({
            'success': True,
            'valid': is_valid,
            'product_info': product_info,
            'can_create_inventory': product_info.get('division_valid', False) if product_info else False
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
