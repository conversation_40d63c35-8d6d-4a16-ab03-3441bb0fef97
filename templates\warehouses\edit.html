{% extends 'base.html' %}

{% block title %}Edit Warehouse - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
<style>
    .main-content {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin: 20px;
        padding: 30px;
    }
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.4);
    }
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    .card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border-radius: 15px 15px 0 0 !important;
        padding: 20px;
    }
    .form-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    .required {
        color: #dc3545;
    }
    .current-image {
        max-height: 200px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="main-content">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="text-primary mb-1">
                    <i class="fas fa-edit"></i> Edit Warehouse
                </h2>
                <p class="text-muted mb-0">Update warehouse information and details</p>
            </div>
            <div>
                <a href="/warehouse-management/manage" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Warehouses
                </a>
            </div>
        </div>

        <!-- Edit Warehouse Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-warehouse"></i> {{ warehouse.name }}</h5>
            </div>
            <div class="card-body">
                <form method="post" action="/warehouse-management/edit/{{ warehouse.warehouse_id }}" enctype="multipart/form-data">
                    
                    <!-- Basic Information -->
                    <h6 class="text-primary mb-3"><i class="fas fa-info-circle"></i> Basic Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Warehouse Name <span class="required">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ warehouse.name }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="city">City <span class="required">*</span></label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="{{ warehouse.city }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="address">Address</label>
                                <textarea class="form-control" id="address" name="address" rows="3">{{ warehouse.address or '' }}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="country">Country</label>
                                <input type="text" class="form-control" id="country" name="country" 
                                       value="{{ warehouse.country or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="capacity">Capacity (sq ft)</label>
                                <input type="number" class="form-control" id="capacity" name="capacity" 
                                       value="{{ warehouse.capacity or '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <hr class="my-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-address-book"></i> Contact Information</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="manager">Manager Name</label>
                                <input type="text" class="form-control" id="manager" name="manager" 
                                       value="{{ warehouse.manager or '' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ warehouse.phone or '' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ warehouse.email or '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="active" {% if warehouse.status == 'active' %}selected{% endif %}>Active</option>
                                    <option value="inactive" {% if warehouse.status == 'inactive' %}selected{% endif %}>Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <hr class="my-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-file-alt"></i> Additional Information</h6>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3">{{ warehouse.description or '' }}</textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Current Image -->
                    {% if warehouse.image_path %}
                    <hr class="my-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-image"></i> Current Image</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <img src="{{ url_for('static', filename=warehouse.image_path) }}" 
                                 alt="{{ warehouse.name }}" class="img-fluid current-image">
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse_image">Upload New Image (Optional)</label>
                                <input type="file" class="form-control-file" id="warehouse_image" name="warehouse_image" accept="image/*">
                                <small class="form-text text-muted">Leave empty to keep current image</small>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <!-- Image Upload -->
                    <hr class="my-4">
                    <h6 class="text-primary mb-3"><i class="fas fa-image"></i> Warehouse Image (Optional)</h6>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="warehouse_image">Upload Image</label>
                                <input type="file" class="form-control-file" id="warehouse_image" name="warehouse_image" accept="image/*">
                                <small class="form-text text-muted">PNG, JPG, JPEG, GIF, WebP files up to 16MB</small>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Form Actions -->
                    <hr class="my-4">
                    <div class="d-flex justify-content-end">
                        <a href="/warehouse-management/manage" class="btn btn-outline-secondary me-3">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Warehouse
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields marked with *');
    }
});
</script>
{% endblock %}
