{% extends 'base.html' %}

{% block title %}Rider Dashboard - Medivent ERP{% endblock %}

{% block content %}
<style>
/* Modern 2025 UI Styles with Project Colors */
.rider-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.dashboard-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboard-title {
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    margin: 0;
}

.rider-info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.rider-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #4e73df, #224abe, #36b9cc);
}

.rider-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4e73df, #36b9cc);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 15px 30px rgba(78, 115, 223, 0.3);
    position: relative;
}

.rider-avatar::after {
    content: '';
    position: absolute;
    inset: 3px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    z-index: -1;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.stat-card.primary::before {
    background: linear-gradient(135deg, #4e73df, #224abe);
}

.stat-card.success::before {
    background: linear-gradient(135deg, #1cc88a, #13855c);
}

.stat-card.info::before {
    background: linear-gradient(135deg, #36b9cc, #258391);
}

.stat-card.warning::before {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #4e73df, #224abe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #5a5c69;
    margin-bottom: 10px;
}

.modern-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 30px;
}

.modern-btn {
    background: linear-gradient(135deg, #4e73df, #224abe);
    border: none;
    border-radius: 15px;
    padding: 12px 30px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(78, 115, 223, 0.3);
}

.modern-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(78, 115, 223, 0.4);
    color: white;
}

.timeline-modern {
    position: relative;
    padding-left: 40px;
}

.timeline-modern::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #4e73df, #36b9cc);
    border-radius: 2px;
}

.timeline-item-modern {
    position: relative;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.timeline-marker-modern {
    position: absolute;
    left: -52px;
    top: 25px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4e73df, #36b9cc);
    box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
}

.glass-table {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.glass-table thead {
    background: linear-gradient(135deg, #4e73df, #224abe);
    color: white;
}

.glass-table th {
    border: none;
    padding: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.85rem;
}

.glass-table td {
    border: none;
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-delivered {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    color: white;
}

.status-dispatched {
    background: linear-gradient(135deg, #4e73df, #224abe);
    color: white;
}

.status-pending {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
    color: white;
}

@media (max-width: 768px) {
    .dashboard-title {
        font-size: 2rem;
    }

    .rider-avatar {
        width: 80px;
        height: 80px;
    }

    .stat-number {
        font-size: 2rem;
    }
}
</style>

<div class="rider-dashboard">
    <div class="container-fluid">
        <!-- Modern Header -->
        <div class="dashboard-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="dashboard-title">
                        <i class="fas fa-motorcycle mr-3"></i>Professional Rider Dashboard
                    </h1>
                    <p class="text-muted mb-0 mt-2">Welcome back! Here's your performance overview</p>
                </div>
                <div>
                    <button class="modern-btn" onclick="refreshDashboard()">
                        <i class="fas fa-sync mr-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Modern Rider Info Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="rider-info-card">
                    <div class="row align-items-center">
                        <div class="col-md-3 text-center">
                            <div class="rider-avatar">
                                <i class="fas fa-user fa-3x text-white"></i>
                            </div>
                            <div class="status-badge status-delivered">
                                <i class="fas fa-circle mr-2"></i>Active
                            </div>
                        </div>
                        <div class="col-md-9">
                            <h2 class="font-weight-bold mb-3" style="color: #4e73df;">
                                {{ rider.name if rider else 'Ahmed Khan' }}
                            </h2>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-id-card text-primary mr-3"></i>
                                        <div>
                                            <small class="text-muted d-block">Rider ID</small>
                                            <strong>{{ rider.rider_id if rider else 'R001' }}</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-motorcycle text-info mr-3"></i>
                                        <div>
                                            <small class="text-muted d-block">Vehicle</small>
                                            <strong>{{ rider.vehicle_type if rider else 'Motorcycle' }}</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-star text-warning mr-3"></i>
                                        <div>
                                            <small class="text-muted d-block">Rating</small>
                                            <strong>{{ "%.1f"|format(rider.rating if rider else 4.8) }}/5.0</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-map-marker-alt text-success mr-3"></i>
                                        <div>
                                            <small class="text-muted d-block">Current Location</small>
                                            <strong>{{ rider.current_location if rider else 'Karachi Main Market' }}</strong>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="openCurrentLocationOnMap('{{ rider.current_location if rider else 'Karachi Main Market' }}')">
                                        <i class="fas fa-external-link-alt"></i> View on Map
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Performance Stats -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card primary">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-label">Today's Orders</div>
                            <div class="stat-number">{{ today_orders|length if today_orders else 5 }}</div>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-clipboard-list fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card success">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-label">Total Deliveries</div>
                            <div class="stat-number">{{ performance_stats.total_deliveries if performance_stats else 127 }}</div>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-shipping-fast fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card info">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-label">Success Rate</div>
                            <div class="stat-number">
                                {% if performance_stats and performance_stats.total_deliveries > 0 %}
                                    {{ "%.0f"|format((performance_stats.completed_deliveries / performance_stats.total_deliveries * 100)) }}%
                                {% else %}
                                    96%
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-check-circle fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card warning">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="stat-label">Average Rating</div>
                            <div class="stat-number">{{ "%.1f"|format(performance_stats.avg_rating if performance_stats and performance_stats.avg_rating else 4.8) }}</div>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-star fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Orders Section -->
        <div class="row">
            <div class="col-lg-8">
                <div class="modern-card">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <h4 class="font-weight-bold mb-0" style="color: #4e73df;">
                            <i class="fas fa-list-alt mr-2"></i>Today's Orders
                        </h4>
                        <span class="badge badge-primary badge-pill px-3 py-2">
                            {{ today_orders|length if today_orders else 5 }} Orders
                        </span>
                    </div>

                    {% if today_orders %}
                    <div class="table-responsive">
                        <table class="glass-table table">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Status</th>
                                    <th>Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in today_orders %}
                                <tr>
                                    <td><strong style="color: #4e73df;">{{ order.order_id }}</strong></td>
                                    <td>{{ order.customer_name or 'Customer Name' }}</td>
                                    <td>
                                        <span class="status-badge status-{% if order.status == 'Delivered' %}delivered{% elif order.status == 'Dispatched' %}dispatched{% else %}pending{% endif %}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                    <td>{{ order.created_at.strftime('%H:%M') if order.created_at else '10:30' }}</td>
                                    <td>
                                        <button class="btn btn-sm modern-btn" onclick="viewOrder('{{ order.order_id }}')">
                                            <i class="fas fa-eye mr-1"></i>View
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <!-- Demo Orders -->
                    <div class="table-responsive">
                        <table class="glass-table table">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Status</th>
                                    <th>Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong style="color: #4e73df;">ORD001</strong></td>
                                    <td>Fatima Sheikh</td>
                                    <td><span class="status-badge status-pending">Ready for Pickup</span></td>
                                    <td>09:30</td>
                                    <td>
                                        <button class="btn btn-sm modern-btn" onclick="viewOrder('ORD001')">
                                            <i class="fas fa-eye mr-1"></i>View
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong style="color: #4e73df;">ORD002</strong></td>
                                    <td>Hassan Ali</td>
                                    <td><span class="status-badge status-dispatched">Dispatched</span></td>
                                    <td>10:15</td>
                                    <td>
                                        <button class="btn btn-sm modern-btn" onclick="viewOrder('ORD002')">
                                            <i class="fas fa-eye mr-1"></i>View
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong style="color: #4e73df;">ORD003</strong></td>
                                    <td>Ayesha Khan</td>
                                    <td><span class="status-badge status-delivered">Delivered</span></td>
                                    <td>11:45</td>
                                    <td>
                                        <button class="btn btn-sm modern-btn" onclick="viewOrder('ORD003')">
                                            <i class="fas fa-eye mr-1"></i>View
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Modern Quick Actions -->
                <div class="modern-card">
                    <h5 class="font-weight-bold mb-4" style="color: #4e73df;">
                        <i class="fas fa-bolt mr-2"></i>Quick Actions
                    </h5>
                    <div class="d-grid gap-3">
                        <a href="{{ url_for('riders_orders') }}" class="modern-btn text-decoration-none d-flex align-items-center">
                            <i class="fas fa-list mr-3"></i>
                            <div>
                                <div class="font-weight-bold">View All Orders</div>
                                <small class="opacity-75">Manage your deliveries</small>
                            </div>
                        </a>
                        <a href="{{ url_for('rider_delivery_routes') }}" class="modern-btn text-decoration-none d-flex align-items-center">
                            <i class="fas fa-route mr-3"></i>
                            <div>
                                <div class="font-weight-bold">Delivery Routes</div>
                                <small class="opacity-75">Optimize your routes</small>
                            </div>
                        </a>
                        <a href="{{ url_for('rider_performance') }}" class="modern-btn text-decoration-none d-flex align-items-center">
                            <i class="fas fa-chart-line mr-3"></i>
                            <div>
                                <div class="font-weight-bold">Performance</div>
                                <small class="opacity-75">View analytics</small>
                            </div>
                        </a>
                        <button onclick="updateLocation()" class="modern-btn d-flex align-items-center">
                            <i class="fas fa-map-marker-alt mr-3"></i>
                            <div>
                                <div class="font-weight-bold">Update Location</div>
                                <small class="opacity-75">Share your position</small>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- Modern Activity Timeline -->
                <div class="modern-card">
                    <h5 class="font-weight-bold mb-4" style="color: #4e73df;">
                        <i class="fas fa-clock mr-2"></i>Recent Activity
                    </h5>
                    <div class="timeline-modern">
                        <div class="timeline-item-modern">
                            <div class="timeline-marker-modern"></div>
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <h6 class="font-weight-bold mb-1" style="color: #1cc88a;">Order Delivered</h6>
                                    <p class="mb-1 text-muted">Successfully delivered order ORD00123</p>
                                    <small class="text-muted">2 hours ago</small>
                                </div>
                                <i class="fas fa-check-circle text-success fa-2x"></i>
                            </div>
                        </div>
                        <div class="timeline-item-modern">
                            <div class="timeline-marker-modern"></div>
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <h6 class="font-weight-bold mb-1" style="color: #4e73df;">Order Picked Up</h6>
                                    <p class="mb-1 text-muted">Picked up order ORD00124 from warehouse</p>
                                    <small class="text-muted">3 hours ago</small>
                                </div>
                                <i class="fas fa-box text-primary fa-2x"></i>
                            </div>
                        </div>
                        <div class="timeline-item-modern">
                            <div class="timeline-marker-modern"></div>
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <h6 class="font-weight-bold mb-1" style="color: #36b9cc;">Shift Started</h6>
                                    <p class="mb-1 text-muted">Started delivery shift for today</p>
                                    <small class="text-muted">8 hours ago</small>
                                </div>
                                <i class="fas fa-play-circle text-info fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Modern Dashboard JavaScript
function refreshDashboard() {
    // Add loading animation
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...';
    btn.disabled = true;

    setTimeout(() => {
        location.reload();
    }, 1000);
}

function viewOrder(orderId) {
    // Add smooth transition effect
    document.body.style.opacity = '0.8';
    setTimeout(() => {
        window.location.href = `/orders/${orderId}`;
    }, 300);
}

function updateLocation() {
    if (navigator.geolocation) {
        // Show modern notification
        showNotification('Getting your location...', 'info');

        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude.toFixed(6);
                const lng = position.coords.longitude.toFixed(6);
                showNotification(`Location updated: ${lat}, ${lng}`, 'success');
            },
            function(error) {
                showNotification('Unable to get location. Please try again.', 'error');
            }
        );
    } else {
        showNotification('Geolocation is not supported by this browser.', 'error');
    }
}

function showNotification(message, type) {
    // Create modern notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        backdrop-filter: blur(10px);
        border: none;
    `;
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add smooth animations on page load
document.addEventListener('DOMContentLoaded', function() {
    // Animate stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animate timeline items
    const timelineItems = document.querySelectorAll('.timeline-item-modern');
    timelineItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(30px)';
        setTimeout(() => {
            item.style.transition = 'all 0.6s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, 500 + (index * 150));
    });
});

// Google Maps integration function for current location
function openCurrentLocationOnMap(location) {
    if (!location || location.trim() === '') {
        showNotification('No location available', 'warning');
        return;
    }

    // Create Google Maps URL with the location
    const encodedLocation = encodeURIComponent(location.trim());
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;

    // Open in new tab
    window.open(googleMapsUrl, '_blank');

    showNotification(`Opening Google Maps for: ${location}`, 'info');
}
</script>
{% endblock %}
