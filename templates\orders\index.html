{% extends 'base.html' %}

{% block title %}Orders Management - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<!-- Order Management Header -->
<div class="bg-primary text-white p-3 mb-0">
    <div class="d-flex justify-content-between align-items-center">
        <h4 class="mb-0">Order Management</h4>
        <div class="d-flex align-items-center">
            <span class="mr-3">📅 Today: 24/06/2025</span>
            <span class="mr-3">👤 User: Over</span>
            <a href="{{ url_for('orders.new_order') }}" class="btn btn-success btn-sm">
                <i class="fas fa-plus"></i> Place New Order
            </a>
            <button class="btn btn-info btn-sm ml-2" onclick="exportOrders()">
                <i class="fas fa-download"></i> Export to Excel
            </button>
        </div>
    </div>
</div>

<!-- Search Section -->
<div class="bg-light p-3 border-bottom">
    <div class="row">
        <div class="col-md-8">
            <div class="input-group">
                <input type="text" class="form-control" id="globalSearch" placeholder="Enter Order ID, Invoice Number, or DC Number...">
                <div class="input-group-append">
                    <button class="btn btn-primary" type="button" onclick="performSearch()">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-right">
            <small class="text-muted">Search by Order ID, Customer, or Phone</small>
        </div>
    </div>
</div>

<!-- Orders Table -->
<div class="bg-white">
    <div class="table-responsive">
        <table class="table table-striped table-hover mb-0" id="ordersTable">
            <thead class="thead-dark">
                <tr>
                    <th style="width: 15%;">Order ID / Invoice #</th>
                    <th style="width: 20%;">Customer</th>
                    <th style="width: 15%;">Date</th>
                    <th style="width: 15%;">Status</th>
                    <th style="width: 15%;">Amount</th>
                    <th style="width: 20%;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for order in orders %}
                <tr>
                    <td>
                        <strong class="text-primary">{{ order.order_id if order.order_id else ('ORD' + order.id|string) }}</strong>
                    </td>
                    <td>{{ order.customer_name }}</td>
                    <td>{{ safe_strftime(order.order_date, '%d/%m/%Y %I:%M %p') }}</td>
                    <td>
                        {% if order.status == 'Placed' or order.status == 'Pending' %}
                            <span class="badge badge-warning px-3 py-1">Pending</span>
                        {% elif order.status == 'Approved' %}
                            <span class="badge badge-primary px-3 py-1">Approved</span>
                        {% elif order.status == 'Processing' %}
                            <span class="badge badge-info px-3 py-1">DC Generated</span>
                        {% elif order.status == 'Dispatched' %}
                            <span class="badge badge-success px-3 py-1">DC Generated</span>
                        {% elif order.status == 'Delivered' %}
                            <span class="badge badge-success px-3 py-1">DC Generated</span>
                        {% elif order.status == 'Cancelled' %}
                            <span class="badge badge-danger px-3 py-1">Cancelled</span>
                        {% else %}
                            <span class="badge badge-secondary px-3 py-1">{{ order.status }}</span>
                        {% endif %}
                    </td>
                    <td>{{ "%.2f"|format(order.order_amount|float) if order.order_amount else '0.00' }}</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button class="btn btn-outline-primary" onclick="viewOrder('{{ order.order_id if order.order_id else order.id }}')" title="View">
                                View
                            </button>
                            {% if order.status in ['Approved', 'Processing', 'Dispatched', 'Delivered'] %}
                            <button class="btn btn-outline-success" onclick="generateDC('{{ order.order_id if order.order_id else order.id }}')" title="Challan">
                                📋 Challan
                            </button>
                            {% endif %}
                            <button class="btn btn-outline-secondary" onclick="workflowOrder('{{ order.order_id if order.order_id else order.id }}')" title="Workflow">
                                Workflow
                            </button>
                        </div>
                    </td>
                </tr>
                {% else %}
                <!-- Empty state when no orders exist -->
                <tr>
                    <td colspan="6" class="text-center py-5">
                        <div class="text-muted">
                            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                            <h5>No Orders Available</h5>
                            <p>No orders have been placed yet. Create your first order to get started.</p>
                            <a href="{{ url_for('new_order') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create New Order
                            </a>
                        </div>
                    </td>
                </tr>

                {% endfor %}
            </tbody>
        </table>
    </div>
</div>


</div>

<script>
$(document).ready(function() {
    // Initialize DataTable with clean styling
    $('#ordersTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[2, "desc"]], // Sort by date
        columnDefs: [
            { orderable: false, targets: [5] } // Disable sorting for actions column
        ],
        dom: 'lrtip', // Remove search box since we have custom search
        language: {
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            }
        }
    });
});

// Search functionality
function performSearch() {
    const searchTerm = $('#globalSearch').val();
    const table = $('#ordersTable').DataTable();
    table.search(searchTerm).draw();

    if (searchTerm) {
        showNotification(`Searching for: ${searchTerm}`, 'info', 2000);
    }
}

// Allow Enter key to trigger search
$('#globalSearch').on('keypress', function(e) {
    if (e.which === 13) {
        performSearch();
    }
});

// Notification function
function showNotification(message, type = 'info', duration = 3000) {
    const notification = $(`
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `);

    $('body').append(notification);

    setTimeout(() => {
        notification.alert('close');
    }, duration);
}

function viewOrder(orderId) {
    // Navigate to order details page
    window.location.href = `/orders/${orderId}`;
}

function generateDC(orderId) {
    if (confirm(`Generate Delivery Challan for order ${orderId}?`)) {
        showNotification(`Starting batch selection for order ${orderId}...`, 'success', 2000);
        window.location.href = `/orders/${orderId}/select-batch`;
    }
}

function workflowOrder(orderId) {
    // Show workflow options
    const workflowOptions = `
        <div class="btn-group-vertical" role="group">
            <button class="btn btn-outline-primary btn-sm" onclick="viewOrder('${orderId}')">
                <i class="fas fa-eye"></i> View Details
            </button>
            <button class="btn btn-outline-success btn-sm" onclick="generateDC('${orderId}')">
                <i class="fas fa-file-alt"></i> Generate DC
            </button>
            <button class="btn btn-outline-warning btn-sm" onclick="approveOrder('${orderId}')">
                <i class="fas fa-check"></i> Approve Order
            </button>
            <button class="btn btn-outline-info btn-sm" onclick="editOrder('${orderId}')">
                <i class="fas fa-edit"></i> Edit Order
            </button>
        </div>
    `;

    // You can implement a dropdown or modal here
    // For now, just show the view order
    viewOrder(orderId);
}

function editOrder(orderId) {
    window.location.href = `/orders/${orderId}/update`;
}

function approveOrder(orderId) {
    if (confirm(`Approve order ${orderId}?`)) {
        showNotification(`Approving order ${orderId}...`, 'success', 2000);
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/orders/${orderId}/approve`;
        document.body.appendChild(form);
        form.submit();
    }
}

function exportOrders() {
    showNotification('Preparing Excel export...', 'info', 2000);
    // Implement export functionality
    setTimeout(() => {
        showNotification('Export completed!', 'success', 2000);
    }, 2000);
}
</script>
{% endblock %}
