#!/usr/bin/env python3
"""
Quick database checker to identify missing tables and schema issues
"""

import sqlite3
import os

def check_database():
    db_path = 'instance/medivent.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;")
        tables = cursor.fetchall()
        
        print("📋 EXISTING TABLES:")
        print("=" * 50)
        for table in tables:
            print(f"✅ {table[0]}")
        
        print(f"\n📊 Total tables: {len(tables)}")
        
        # Check for specific missing tables
        table_names = [table[0] for table in tables]
        
        missing_tables = []
        required_tables = ['rider_bikes', 'rider_performance_logs', 'delivery_challans']
        
        print(f"\n🔍 CHECKING REQUIRED TABLES:")
        print("=" * 50)
        for required_table in required_tables:
            if required_table in table_names:
                print(f"✅ {required_table} - EXISTS")
            else:
                print(f"❌ {required_table} - MISSING")
                missing_tables.append(required_table)
        
        # Check delivery_challans table structure if it exists
        if 'delivery_challans' in table_names:
            print(f"\n📋 DELIVERY_CHALLANS TABLE STRUCTURE:")
            print("=" * 50)
            cursor.execute("PRAGMA table_info(delivery_challans);")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
        
        # Check for date columns that might be causing strftime issues
        if 'delivery_challans' in table_names:
            print(f"\n🔍 SAMPLE DELIVERY_CHALLANS DATA:")
            print("=" * 50)
            cursor.execute("SELECT * FROM delivery_challans LIMIT 3;")
            rows = cursor.fetchall()
            if rows:
                cursor.execute("PRAGMA table_info(delivery_challans);")
                columns = [col[1] for col in cursor.fetchall()]
                for i, row in enumerate(rows):
                    print(f"Row {i+1}:")
                    for j, value in enumerate(row):
                        if j < len(columns):
                            print(f"  {columns[j]}: {value} ({type(value).__name__})")
                    print()
            else:
                print("  No data found")
        
        conn.close()
        
        if missing_tables:
            print(f"\n⚠️  MISSING TABLES FOUND: {', '.join(missing_tables)}")
            return missing_tables
        else:
            print(f"\n✅ All required tables exist!")
            return []
            
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return None

if __name__ == "__main__":
    check_database()
