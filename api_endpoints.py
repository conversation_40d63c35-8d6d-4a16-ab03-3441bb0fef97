#!/usr/bin/env python3
"""
Comprehensive API Endpoints for ERP System
Missing API endpoints with RESTful conventions, proper error handling, authentication, and JSON response formatting
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from utils.db import get_db
from datetime import datetime, timedelta
import json
import sqlite3

# Create API blueprint
api_bp = Blueprint('api', __name__, url_prefix='/api')

# ============================================================================
# CUSTOMERS API - Complete CRUD Operations
# ============================================================================

@api_bp.route('/customers', methods=['GET'])
@login_required
def get_customers():
    """Get customers with pagination, search, and filtering"""
    try:
        db = get_db()
        
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)  # Max 100 per page
        search = request.args.get('search', '').strip()
        city = request.args.get('city', '').strip()
        status = request.args.get('status', '').strip()
        sort_by = request.args.get('sort_by', 'name')
        sort_order = request.args.get('sort_order', 'asc')
        
        # Build query
        where_conditions = []
        params = []
        
        if search:
            where_conditions.append("(name LIKE ? OR email LIKE ? OR phone LIKE ?)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])
        
        if city:
            where_conditions.append("city = ?")
            params.append(city)
            
        if status:
            where_conditions.append("status = ?")
            params.append(status)
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # Validate sort parameters
        valid_sort_fields = ['name', 'email', 'city', 'created_at', 'credit_limit']
        if sort_by not in valid_sort_fields:
            sort_by = 'name'
        if sort_order.lower() not in ['asc', 'desc']:
            sort_order = 'asc'
        
        # Get total count
        count_query = f"SELECT COUNT(*) as total FROM customers WHERE {where_clause}"
        total_count = db.execute(count_query, params).fetchone()['total']
        
        # Get customers with pagination
        offset = (page - 1) * per_page
        customers_query = f"""
            SELECT customer_id, name, email, phone, city, address, status, 
                   credit_limit, outstanding_balance, created_at, updated_at
            FROM customers 
            WHERE {where_clause}
            ORDER BY {sort_by} {sort_order}
            LIMIT ? OFFSET ?
        """
        params.extend([per_page, offset])
        
        customers = db.execute(customers_query, params).fetchall()
        
        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page
        has_next = page < total_pages
        has_prev = page > 1
        
        return jsonify({
            'success': True,
            'data': [dict(customer) for customer in customers],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': total_pages,
                'has_next': has_next,
                'has_prev': has_prev
            },
            'filters': {
                'search': search,
                'city': city,
                'status': status,
                'sort_by': sort_by,
                'sort_order': sort_order
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"Error in get_customers API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve customers',
            'message': str(e)
        }), 500

@api_bp.route('/customers', methods=['POST'])
@login_required
def create_customer():
    """Create a new customer"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No JSON data provided'
            }), 400
        
        # Validate required fields
        required_fields = ['name', 'email', 'phone']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            return jsonify({
                'success': False,
                'error': f'Missing required fields: {", ".join(missing_fields)}'
            }), 400
        
        db = get_db()
        
        # Check if customer already exists
        existing_customer = db.execute(
            'SELECT customer_id FROM customers WHERE email = ? OR phone = ?',
            (data['email'], data['phone'])
        ).fetchone()
        
        if existing_customer:
            return jsonify({
                'success': False,
                'error': 'Customer with this email or phone already exists'
            }), 409
        
        # Generate customer ID
        customer_id = f"CUST{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Insert customer
        db.execute('''
            INSERT INTO customers (
                customer_id, name, email, phone, city, address, status,
                credit_limit, outstanding_balance, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            customer_id,
            data['name'],
            data['email'],
            data['phone'],
            data.get('city', ''),
            data.get('address', ''),
            data.get('status', 'active'),
            float(data.get('credit_limit', 0)),
            0.0,  # outstanding_balance starts at 0
            datetime.now(),
            datetime.now()
        ))
        
        db.commit()
        
        # Get the created customer
        customer = db.execute(
            'SELECT * FROM customers WHERE customer_id = ?',
            (customer_id,)
        ).fetchone()
        
        return jsonify({
            'success': True,
            'message': 'Customer created successfully',
            'data': dict(customer)
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error in create_customer API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to create customer',
            'message': str(e)
        }), 500

@api_bp.route('/customers/<customer_id>', methods=['GET'])
@login_required
def get_customer(customer_id):
    """Get a specific customer by ID"""
    try:
        db = get_db()
        
        customer = db.execute(
            'SELECT * FROM customers WHERE customer_id = ?',
            (customer_id,)
        ).fetchone()
        
        if not customer:
            return jsonify({
                'success': False,
                'error': 'Customer not found'
            }), 404
        
        # Get customer's order history
        orders = db.execute('''
            SELECT order_id, order_date, status, order_amount
            FROM orders 
            WHERE customer_name = ?
            ORDER BY order_date DESC
            LIMIT 10
        ''', (customer['name'],)).fetchall()
        
        customer_data = dict(customer)
        customer_data['recent_orders'] = [dict(order) for order in orders]
        
        return jsonify({
            'success': True,
            'data': customer_data
        })
        
    except Exception as e:
        current_app.logger.error(f"Error in get_customer API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve customer',
            'message': str(e)
        }), 500

@api_bp.route('/customers/<customer_id>', methods=['PUT'])
@login_required
def update_customer(customer_id):
    """Update a customer"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No JSON data provided'
            }), 400
        
        db = get_db()
        
        # Check if customer exists
        customer = db.execute(
            'SELECT customer_id FROM customers WHERE customer_id = ?',
            (customer_id,)
        ).fetchone()
        
        if not customer:
            return jsonify({
                'success': False,
                'error': 'Customer not found'
            }), 404
        
        # Build update query dynamically
        update_fields = []
        params = []
        
        updatable_fields = ['name', 'email', 'phone', 'city', 'address', 'status', 'credit_limit']
        
        for field in updatable_fields:
            if field in data:
                update_fields.append(f"{field} = ?")
                params.append(data[field])
        
        if not update_fields:
            return jsonify({
                'success': False,
                'error': 'No valid fields to update'
            }), 400
        
        # Add updated_at
        update_fields.append("updated_at = ?")
        params.append(datetime.now())
        params.append(customer_id)
        
        update_query = f"UPDATE customers SET {', '.join(update_fields)} WHERE customer_id = ?"
        
        db.execute(update_query, params)
        db.commit()
        
        # Get updated customer
        updated_customer = db.execute(
            'SELECT * FROM customers WHERE customer_id = ?',
            (customer_id,)
        ).fetchone()
        
        return jsonify({
            'success': True,
            'message': 'Customer updated successfully',
            'data': dict(updated_customer)
        })
        
    except Exception as e:
        current_app.logger.error(f"Error in update_customer API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to update customer',
            'message': str(e)
        }), 500

@api_bp.route('/customers/<customer_id>', methods=['DELETE'])
@login_required
def delete_customer(customer_id):
    """Delete a customer (soft delete by setting status to inactive)"""
    try:
        db = get_db()
        
        # Check if customer exists
        customer = db.execute(
            'SELECT customer_id, name FROM customers WHERE customer_id = ?',
            (customer_id,)
        ).fetchone()
        
        if not customer:
            return jsonify({
                'success': False,
                'error': 'Customer not found'
            }), 404
        
        # Check if customer has active orders
        active_orders = db.execute(
            'SELECT COUNT(*) as count FROM orders WHERE customer_name = ? AND status NOT IN ("Delivered", "Cancelled")',
            (customer['name'],)
        ).fetchone()['count']
        
        if active_orders > 0:
            return jsonify({
                'success': False,
                'error': f'Cannot delete customer with {active_orders} active orders'
            }), 409
        
        # Soft delete - set status to inactive
        db.execute(
            'UPDATE customers SET status = ?, updated_at = ? WHERE customer_id = ?',
            ('inactive', datetime.now(), customer_id)
        )
        db.commit()
        
        return jsonify({
            'success': True,
            'message': 'Customer deactivated successfully'
        })
        
    except Exception as e:
        current_app.logger.error(f"Error in delete_customer API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to delete customer',
            'message': str(e)
        }), 500

# ============================================================================
# INVENTORY API - Complete Stock Management Operations
# ============================================================================

@api_bp.route('/inventory', methods=['GET'])
@login_required
def get_inventory():
    """Get inventory with pagination, search, and filtering"""
    try:
        db = get_db()

        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        search = request.args.get('search', '').strip()
        warehouse_id = request.args.get('warehouse_id', '').strip()
        low_stock_only = request.args.get('low_stock_only', 'false').lower() == 'true'
        category = request.args.get('category', '').strip()
        sort_by = request.args.get('sort_by', 'product_name')
        sort_order = request.args.get('sort_order', 'asc')

        # Build query
        where_conditions = ["i.quantity >= 0"]  # Base condition
        params = []

        if search:
            where_conditions.append("(p.name LIKE ? OR p.product_id LIKE ?)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param])

        if warehouse_id:
            where_conditions.append("i.warehouse_id = ?")
            params.append(warehouse_id)

        if category:
            where_conditions.append("p.category = ?")
            params.append(category)

        if low_stock_only:
            where_conditions.append("i.quantity <= p.min_stock_level")

        where_clause = " AND ".join(where_conditions)

        # Validate sort parameters
        valid_sort_fields = ['product_name', 'quantity', 'location', 'last_updated']
        if sort_by not in valid_sort_fields:
            sort_by = 'product_name'
        if sort_order.lower() not in ['asc', 'desc']:
            sort_order = 'asc'

        # Get total count
        count_query = f"""
            SELECT COUNT(*) as total
            FROM inventory i
            LEFT JOIN products p ON i.product_id = p.product_id
            WHERE {where_clause}
        """
        total_count = db.execute(count_query, params).fetchone()['total']

        # Get inventory with pagination
        offset = (page - 1) * per_page
        inventory_query = f"""
            SELECT i.inventory_id, i.product_id, p.name as product_name,
                   p.category, p.unit_price, p.min_stock_level,
                   i.quantity, i.location, i.warehouse_id, i.last_updated,
                   w.name as warehouse_name,
                   CASE
                       WHEN i.quantity <= p.min_stock_level THEN 'low'
                       WHEN i.quantity <= p.min_stock_level * 2 THEN 'medium'
                       ELSE 'good'
                   END as stock_status
            FROM inventory i
            LEFT JOIN products p ON i.product_id = p.product_id
            LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE {where_clause}
            ORDER BY {sort_by} {sort_order}
            LIMIT ? OFFSET ?
        """
        params.extend([per_page, offset])

        inventory_items = db.execute(inventory_query, params).fetchall()

        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page
        has_next = page < total_pages
        has_prev = page > 1

        # Get summary statistics
        stats_query = """
            SELECT
                COUNT(*) as total_items,
                SUM(i.quantity) as total_quantity,
                COUNT(CASE WHEN i.quantity <= p.min_stock_level THEN 1 END) as low_stock_items,
                COUNT(DISTINCT i.warehouse_id) as warehouses_count
            FROM inventory i
            LEFT JOIN products p ON i.product_id = p.product_id
        """
        stats = db.execute(stats_query).fetchone()

        return jsonify({
            'success': True,
            'data': [dict(item) for item in inventory_items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': total_pages,
                'has_next': has_next,
                'has_prev': has_prev
            },
            'statistics': dict(stats),
            'filters': {
                'search': search,
                'warehouse_id': warehouse_id,
                'low_stock_only': low_stock_only,
                'category': category,
                'sort_by': sort_by,
                'sort_order': sort_order
            }
        })

    except Exception as e:
        current_app.logger.error(f"Error in get_inventory API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve inventory',
            'message': str(e)
        }), 500

@api_bp.route('/inventory', methods=['POST'])
@login_required
def create_inventory_item():
    """Create a new inventory item"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'No JSON data provided'
            }), 400

        # Validate required fields
        required_fields = ['product_id', 'quantity', 'warehouse_id']
        missing_fields = [field for field in required_fields if field not in data]

        if missing_fields:
            return jsonify({
                'success': False,
                'error': f'Missing required fields: {", ".join(missing_fields)}'
            }), 400

        db = get_db()

        # Validate product exists
        product = db.execute(
            'SELECT product_id, name FROM products WHERE product_id = ?',
            (data['product_id'],)
        ).fetchone()

        if not product:
            return jsonify({
                'success': False,
                'error': 'Product not found'
            }), 404

        # Validate warehouse exists
        warehouse = db.execute(
            'SELECT warehouse_id FROM warehouses WHERE warehouse_id = ?',
            (data['warehouse_id'],)
        ).fetchone()

        if not warehouse:
            return jsonify({
                'success': False,
                'error': 'Warehouse not found'
            }), 404

        # Check if inventory item already exists
        existing_item = db.execute(
            'SELECT inventory_id FROM inventory WHERE product_id = ? AND warehouse_id = ?',
            (data['product_id'], data['warehouse_id'])
        ).fetchone()

        if existing_item:
            return jsonify({
                'success': False,
                'error': 'Inventory item already exists for this product and warehouse'
            }), 409

        # Generate inventory ID
        inventory_id = f"INV{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # Insert inventory item
        db.execute('''
            INSERT INTO inventory (
                inventory_id, product_id, quantity, location, warehouse_id, last_updated
            ) VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            inventory_id,
            data['product_id'],
            int(data['quantity']),
            data.get('location', ''),
            data['warehouse_id'],
            datetime.now()
        ))

        db.commit()

        # Get the created inventory item with product details
        inventory_item = db.execute('''
            SELECT i.*, p.name as product_name, w.name as warehouse_name
            FROM inventory i
            LEFT JOIN products p ON i.product_id = p.product_id
            LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE i.inventory_id = ?
        ''', (inventory_id,)).fetchone()

        return jsonify({
            'success': True,
            'message': 'Inventory item created successfully',
            'data': dict(inventory_item)
        }), 201

    except Exception as e:
        current_app.logger.error(f"Error in create_inventory_item API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to create inventory item',
            'message': str(e)
        }), 500

@api_bp.route('/inventory/<inventory_id>', methods=['PUT'])
@login_required
def update_inventory_item(inventory_id):
    """Update inventory item quantity and location"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'No JSON data provided'
            }), 400

        db = get_db()

        # Check if inventory item exists
        inventory_item = db.execute(
            'SELECT * FROM inventory WHERE inventory_id = ?',
            (inventory_id,)
        ).fetchone()

        if not inventory_item:
            return jsonify({
                'success': False,
                'error': 'Inventory item not found'
            }), 404

        # Build update query
        update_fields = []
        params = []

        if 'quantity' in data:
            update_fields.append("quantity = ?")
            params.append(int(data['quantity']))

        if 'location' in data:
            update_fields.append("location = ?")
            params.append(data['location'])

        if not update_fields:
            return jsonify({
                'success': False,
                'error': 'No valid fields to update'
            }), 400

        # Add last_updated
        update_fields.append("last_updated = ?")
        params.append(datetime.now())
        params.append(inventory_id)

        update_query = f"UPDATE inventory SET {', '.join(update_fields)} WHERE inventory_id = ?"

        db.execute(update_query, params)
        db.commit()

        # Get updated inventory item
        updated_item = db.execute('''
            SELECT i.*, p.name as product_name, w.name as warehouse_name
            FROM inventory i
            LEFT JOIN products p ON i.product_id = p.product_id
            LEFT JOIN warehouses w ON i.warehouse_id = w.warehouse_id
            WHERE i.inventory_id = ?
        ''', (inventory_id,)).fetchone()

        return jsonify({
            'success': True,
            'message': 'Inventory item updated successfully',
            'data': dict(updated_item)
        })

    except Exception as e:
        current_app.logger.error(f"Error in update_inventory_item API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to update inventory item',
            'message': str(e)
        }), 500

@api_bp.route('/inventory/stock-movement', methods=['POST'])
@login_required
def record_stock_movement():
    """Record stock movement (in/out/transfer)"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'No JSON data provided'
            }), 400

        # Validate required fields
        required_fields = ['inventory_id', 'movement_type', 'quantity', 'reason']
        missing_fields = [field for field in required_fields if field not in data]

        if missing_fields:
            return jsonify({
                'success': False,
                'error': f'Missing required fields: {", ".join(missing_fields)}'
            }), 400

        movement_type = data['movement_type']
        if movement_type not in ['in', 'out', 'transfer', 'adjustment']:
            return jsonify({
                'success': False,
                'error': 'Invalid movement_type. Must be: in, out, transfer, or adjustment'
            }), 400

        db = get_db()

        # Get current inventory
        inventory_item = db.execute(
            'SELECT * FROM inventory WHERE inventory_id = ?',
            (data['inventory_id'],)
        ).fetchone()

        if not inventory_item:
            return jsonify({
                'success': False,
                'error': 'Inventory item not found'
            }), 404

        quantity_change = int(data['quantity'])
        current_quantity = inventory_item['quantity']

        # Calculate new quantity based on movement type
        if movement_type == 'in':
            new_quantity = current_quantity + quantity_change
        elif movement_type == 'out':
            new_quantity = current_quantity - quantity_change
            if new_quantity < 0:
                return jsonify({
                    'success': False,
                    'error': f'Insufficient stock. Current: {current_quantity}, Requested: {quantity_change}'
                }), 400
        elif movement_type == 'adjustment':
            new_quantity = quantity_change  # Direct set to new quantity
        else:  # transfer
            new_quantity = current_quantity - quantity_change
            if new_quantity < 0:
                return jsonify({
                    'success': False,
                    'error': f'Insufficient stock for transfer. Current: {current_quantity}, Requested: {quantity_change}'
                }), 400

        # Update inventory quantity
        db.execute(
            'UPDATE inventory SET quantity = ?, last_updated = ? WHERE inventory_id = ?',
            (new_quantity, datetime.now(), data['inventory_id'])
        )

        # Record stock movement
        movement_id = f"MOV{datetime.now().strftime('%Y%m%d%H%M%S')}"
        db.execute('''
            INSERT INTO stock_movements (
                movement_id, inventory_id, movement_type, quantity_change,
                previous_quantity, new_quantity, reason, reference_number,
                created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            movement_id,
            data['inventory_id'],
            movement_type,
            quantity_change if movement_type != 'adjustment' else new_quantity - current_quantity,
            current_quantity,
            new_quantity,
            data['reason'],
            data.get('reference_number', ''),
            current_user.username,
            datetime.now()
        ))

        db.commit()

        return jsonify({
            'success': True,
            'message': 'Stock movement recorded successfully',
            'data': {
                'movement_id': movement_id,
                'previous_quantity': current_quantity,
                'new_quantity': new_quantity,
                'quantity_change': new_quantity - current_quantity
            }
        })

    except Exception as e:
        current_app.logger.error(f"Error in record_stock_movement API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to record stock movement',
            'message': str(e)
        }), 500

# ============================================================================
# REPORTS API - Comprehensive Reporting System
# ============================================================================

@api_bp.route('/reports', methods=['GET'])
@login_required
def get_available_reports():
    """Get list of available reports"""
    try:
        reports = [
            {
                'id': 'sales_summary',
                'name': 'Sales Summary Report',
                'description': 'Overview of sales performance',
                'category': 'sales',
                'parameters': ['date_from', 'date_to', 'division_id']
            },
            {
                'id': 'inventory_status',
                'name': 'Inventory Status Report',
                'description': 'Current inventory levels and stock status',
                'category': 'inventory',
                'parameters': ['warehouse_id', 'low_stock_only']
            },
            {
                'id': 'customer_analysis',
                'name': 'Customer Analysis Report',
                'description': 'Customer behavior and purchase patterns',
                'category': 'customers',
                'parameters': ['date_from', 'date_to', 'city']
            },
            {
                'id': 'financial_summary',
                'name': 'Financial Summary Report',
                'description': 'Revenue, expenses, and profitability analysis',
                'category': 'finance',
                'parameters': ['date_from', 'date_to', 'include_projections']
            },
            {
                'id': 'order_performance',
                'name': 'Order Performance Report',
                'description': 'Order processing and delivery metrics',
                'category': 'operations',
                'parameters': ['date_from', 'date_to', 'status']
            }
        ]

        return jsonify({
            'success': True,
            'data': reports
        })

    except Exception as e:
        current_app.logger.error(f"Error in get_available_reports API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve available reports',
            'message': str(e)
        }), 500

@api_bp.route('/reports/<report_id>', methods=['POST'])
@login_required
def generate_report(report_id):
    """Generate a specific report with parameters"""
    try:
        data = request.get_json() or {}
        db = get_db()

        if report_id == 'sales_summary':
            return generate_sales_summary_report(db, data)
        elif report_id == 'inventory_status':
            return generate_inventory_status_report(db, data)
        elif report_id == 'customer_analysis':
            return generate_customer_analysis_report(db, data)
        elif report_id == 'financial_summary':
            return generate_financial_summary_report(db, data)
        elif report_id == 'order_performance':
            return generate_order_performance_report(db, data)
        else:
            return jsonify({
                'success': False,
                'error': 'Report not found'
            }), 404

    except Exception as e:
        current_app.logger.error(f"Error in generate_report API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to generate report',
            'message': str(e)
        }), 500

# ============================================================================
# USERS API - User Management Operations
# ============================================================================

@api_bp.route('/users', methods=['GET'])
@login_required
def get_users():
    """Get users with pagination and filtering"""
    try:
        db = get_db()

        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        search = request.args.get('search', '').strip()
        role = request.args.get('role', '').strip()
        status = request.args.get('status', '').strip()
        sort_by = request.args.get('sort_by', 'username')
        sort_order = request.args.get('sort_order', 'asc')

        # Build query
        where_conditions = []
        params = []

        if search:
            where_conditions.append("(username LIKE ? OR full_name LIKE ? OR email LIKE ?)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])

        if role:
            where_conditions.append("role = ?")
            params.append(role)

        if status:
            where_conditions.append("status = ?")
            params.append(status)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # Validate sort parameters
        valid_sort_fields = ['username', 'full_name', 'email', 'role', 'status', 'created_at', 'last_login']
        if sort_by not in valid_sort_fields:
            sort_by = 'username'
        if sort_order.lower() not in ['asc', 'desc']:
            sort_order = 'asc'

        # Get total count
        count_query = f"SELECT COUNT(*) as total FROM users WHERE {where_clause}"
        total_count = db.execute(count_query, params).fetchone()['total']

        # Get users with pagination (exclude password_hash)
        offset = (page - 1) * per_page
        users_query = f"""
            SELECT id, username, full_name, email, role, status,
                   created_at, updated_at, last_login
            FROM users
            WHERE {where_clause}
            ORDER BY {sort_by} {sort_order}
            LIMIT ? OFFSET ?
        """
        params.extend([per_page, offset])

        users = db.execute(users_query, params).fetchall()

        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page
        has_next = page < total_pages
        has_prev = page > 1

        # Get role statistics
        role_stats = db.execute('''
            SELECT role, COUNT(*) as count
            FROM users
            GROUP BY role
            ORDER BY count DESC
        ''').fetchall()

        return jsonify({
            'success': True,
            'data': [dict(user) for user in users],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': total_pages,
                'has_next': has_next,
                'has_prev': has_prev
            },
            'statistics': {
                'role_distribution': [dict(stat) for stat in role_stats]
            },
            'filters': {
                'search': search,
                'role': role,
                'status': status,
                'sort_by': sort_by,
                'sort_order': sort_order
            }
        })

    except Exception as e:
        current_app.logger.error(f"Error in get_users API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve users',
            'message': str(e)
        }), 500

@api_bp.route('/users', methods=['POST'])
@login_required
def create_user():
    """Create a new user"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'No JSON data provided'
            }), 400

        # Validate required fields
        required_fields = ['username', 'password', 'full_name', 'email', 'role']
        missing_fields = [field for field in required_fields if not data.get(field)]

        if missing_fields:
            return jsonify({
                'success': False,
                'error': f'Missing required fields: {", ".join(missing_fields)}'
            }), 400

        # Validate role
        valid_roles = ['admin', 'manager', 'sales', 'warehouse', 'rider', 'user']
        if data['role'] not in valid_roles:
            return jsonify({
                'success': False,
                'error': f'Invalid role. Must be one of: {", ".join(valid_roles)}'
            }), 400

        db = get_db()

        # Check if username or email already exists
        existing_user = db.execute(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            (data['username'], data['email'])
        ).fetchone()

        if existing_user:
            return jsonify({
                'success': False,
                'error': 'User with this username or email already exists'
            }), 409

        # Hash password
        from werkzeug.security import generate_password_hash
        password_hash = generate_password_hash(data['password'])

        # Insert user
        cursor = db.execute('''
            INSERT INTO users (
                username, password_hash, full_name, email, role, status,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['username'],
            password_hash,
            data['full_name'],
            data['email'],
            data['role'],
            data.get('status', 'active'),
            datetime.now(),
            datetime.now()
        ))

        user_id = cursor.lastrowid
        db.commit()

        # Get the created user (exclude password_hash)
        user = db.execute('''
            SELECT id, username, full_name, email, role, status, created_at, updated_at
            FROM users WHERE id = ?
        ''', (user_id,)).fetchone()

        return jsonify({
            'success': True,
            'message': 'User created successfully',
            'data': dict(user)
        }), 201

    except Exception as e:
        current_app.logger.error(f"Error in create_user API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to create user',
            'message': str(e)
        }), 500

@api_bp.route('/users/<int:user_id>', methods=['GET'])
@login_required
def get_user(user_id):
    """Get a specific user by ID"""
    try:
        db = get_db()

        user = db.execute('''
            SELECT id, username, full_name, email, role, status,
                   created_at, updated_at, last_login
            FROM users WHERE id = ?
        ''', (user_id,)).fetchone()

        if not user:
            return jsonify({
                'success': False,
                'error': 'User not found'
            }), 404

        # Get user's recent activity
        recent_activity = db.execute('''
            SELECT action, details, timestamp
            FROM activity_logs
            WHERE username = ?
            ORDER BY timestamp DESC
            LIMIT 10
        ''', (user['username'],)).fetchall()

        user_data = dict(user)
        user_data['recent_activity'] = [dict(activity) for activity in recent_activity]

        return jsonify({
            'success': True,
            'data': user_data
        })

    except Exception as e:
        current_app.logger.error(f"Error in get_user API: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve user',
            'message': str(e)
        }), 500

# Helper functions for report generation
def generate_sales_summary_report(db, params):
    """Generate sales summary report"""
    date_from = params.get('date_from', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = params.get('date_to', datetime.now().strftime('%Y-%m-%d'))

    # Get sales data
    sales_data = db.execute('''
        SELECT
            DATE(order_date) as date,
            COUNT(*) as orders_count,
            SUM(order_amount) as total_amount,
            AVG(order_amount) as avg_order_value
        FROM orders
        WHERE order_date BETWEEN ? AND ?
        GROUP BY DATE(order_date)
        ORDER BY date
    ''', (date_from, date_to)).fetchall()

    # Get summary statistics
    summary = db.execute('''
        SELECT
            COUNT(*) as total_orders,
            SUM(order_amount) as total_revenue,
            AVG(order_amount) as avg_order_value,
            COUNT(DISTINCT customer_name) as unique_customers
        FROM orders
        WHERE order_date BETWEEN ? AND ?
    ''', (date_from, date_to)).fetchone()

    return jsonify({
        'success': True,
        'report_type': 'sales_summary',
        'parameters': {'date_from': date_from, 'date_to': date_to},
        'data': {
            'daily_sales': [dict(row) for row in sales_data],
            'summary': dict(summary)
        },
        'generated_at': datetime.now().isoformat()
    })

def generate_inventory_status_report(db, params):
    """Generate inventory status report"""
    warehouse_id = params.get('warehouse_id')
    low_stock_only = params.get('low_stock_only', False)

    where_conditions = []
    query_params = []

    if warehouse_id:
        where_conditions.append("i.warehouse_id = ?")
        query_params.append(warehouse_id)

    if low_stock_only:
        where_conditions.append("i.quantity <= p.min_stock_level")

    where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

    inventory_data = db.execute(f'''
        SELECT
            p.name as product_name,
            p.category,
            i.quantity,
            p.min_stock_level,
            i.location,
            w.name as warehouse_name,
            CASE
                WHEN i.quantity <= p.min_stock_level THEN 'Critical'
                WHEN i.quantity <= p.min_stock_level * 2 THEN 'Low'
                ELSE 'Good'
            END as stock_status
        FROM inventory i
        JOIN products p ON i.product_id = p.product_id
        JOIN warehouses w ON i.warehouse_id = w.warehouse_id
        WHERE {where_clause}
        ORDER BY stock_status, p.name
    ''', query_params).fetchall()

    return jsonify({
        'success': True,
        'report_type': 'inventory_status',
        'parameters': params,
        'data': [dict(row) for row in inventory_data],
        'generated_at': datetime.now().isoformat()
    })

def generate_customer_analysis_report(db, params):
    """Generate customer analysis report"""
    date_from = params.get('date_from', (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d'))
    date_to = params.get('date_to', datetime.now().strftime('%Y-%m-%d'))

    customer_data = db.execute('''
        SELECT
            c.name,
            c.city,
            COUNT(o.order_id) as total_orders,
            SUM(o.order_amount) as total_spent,
            AVG(o.order_amount) as avg_order_value,
            MAX(o.order_date) as last_order_date
        FROM customers c
        LEFT JOIN orders o ON c.name = o.customer_name
        WHERE o.order_date BETWEEN ? AND ?
        GROUP BY c.customer_id
        ORDER BY total_spent DESC
    ''', (date_from, date_to)).fetchall()

    return jsonify({
        'success': True,
        'report_type': 'customer_analysis',
        'parameters': {'date_from': date_from, 'date_to': date_to},
        'data': [dict(row) for row in customer_data],
        'generated_at': datetime.now().isoformat()
    })

def generate_financial_summary_report(db, params):
    """Generate financial summary report"""
    date_from = params.get('date_from', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = params.get('date_to', datetime.now().strftime('%Y-%m-%d'))

    financial_data = db.execute('''
        SELECT
            SUM(order_amount) as total_revenue,
            COUNT(*) as total_transactions,
            AVG(order_amount) as avg_transaction_value
        FROM orders
        WHERE order_date BETWEEN ? AND ?
    ''', (date_from, date_to)).fetchone()

    return jsonify({
        'success': True,
        'report_type': 'financial_summary',
        'parameters': {'date_from': date_from, 'date_to': date_to},
        'data': dict(financial_data),
        'generated_at': datetime.now().isoformat()
    })

def generate_order_performance_report(db, params):
    """Generate order performance report"""
    date_from = params.get('date_from', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_to = params.get('date_to', datetime.now().strftime('%Y-%m-%d'))

    performance_data = db.execute('''
        SELECT
            status,
            COUNT(*) as count,
            AVG(order_amount) as avg_amount
        FROM orders
        WHERE order_date BETWEEN ? AND ?
        GROUP BY status
        ORDER BY count DESC
    ''', (date_from, date_to)).fetchall()

    return jsonify({
        'success': True,
        'report_type': 'order_performance',
        'parameters': {'date_from': date_from, 'date_to': date_to},
        'data': [dict(row) for row in performance_data],
        'generated_at': datetime.now().isoformat()
    })
